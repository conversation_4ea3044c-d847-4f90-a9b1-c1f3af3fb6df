<!-- 价格询价确认页面 -->
<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Input as hInput,
  Radio as hRadio,
  RadioGroup as hRadioGroup,
  InputNumber as hInputNumber,
  Tooltip as hTooltip,
  Modal as hModal,
  Upload as hUpload,
  message,
  FormItem as hFormItem,
  Form as hForm,
} from 'ant-design-vue';
import {
  IPriceInquiry,
  SeasonTypeConstant,
  QuarterSettingTypeConstant,
  PriceTierSettingTypeConstant,
  RoomTypeConstant,
  PriceTypeConstant,
  FileTypeConstant,
  TableShapeConstant,
} from '@haierbusiness-front/common-libs';
import { ref, onMounted, h, computed, inject, Ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { CheckCircleFilled, CloseCircleFilled, QuestionCircleFilled, UploadOutlined } from '@ant-design/icons-vue';
import { priceInquiryApi, fileApi } from '@haierbusiness-front/apis';
import { resolveParam, routerParam } from '@haierbusiness-front/utils';

const route = useRoute();
const router = useRouter();
const record = resolveParam(route.query.record as string);
const hideBtn = record?.hideBtn || '';
// console.log('record', record);
const frameModel = inject<any>('frameModel');
if (frameModel) {
  frameModel.value = hideBtn === '1' ? 1 : 0;
}

const priceInquiryId = ref<number | null>(null);
// 审批流程相关
const businessProcess = import.meta.env.VITE_BUSINESS_PROCESS_URL;
const approvalModalShow = ref(false);
const approveCode = ref<string>(''); // 审批流程Code
const priceInquiryCode = ref<string | null>(null);
const priceInquiryData = ref<IPriceInquiry | null>(null);
const loading = ref(false);
const enableQuarterSetting = ref(true);
const enablejudgment = ref(true);
const resourceHotelQuarters = ref();
const enableStairsSetting = ref(true);
const showSubmitActions = ref(false);
//是否青岛
const isLocal = ref<Boolean | undefined>(false);

// 手动调价相关状态
const manualAdjustVisible = ref(false);
const currentItem = ref<any>(null);
const originalItem = ref<any>(null);
const itemType = ref<number>(1); // 1: 房间, 2: 会场
const marketPrice = ref<number | null>(null);
const fullDayMarketPrice = ref<number | null>(null);
const halfDayMarketPrice = ref<number | null>(null);
const fileList = ref<any[]>([]);
const uploadLoading = ref<boolean>(false);

// 表单验证引用
const formRef = ref<any>(null);

//图片查看
const previewVisible = ref(false);
let previewImage = ref('');
const previewTitle = ref('预览');
const previewImages = ref<Array<{ src: string; title?: string }>>([]);

// 表单数据模型
const formModel = ref({
  marketPrice: null as number | null,
  fullDayMarketPrice: null as number | null,
  halfDayMarketPrice: null as number | null,
  fileList: [] as any[],
});

// 表单验证规则
// const formRules = computed(() => {
//   const rules: any = {
//     fileList: [{ validator: validateFileList, trigger: 'change' }],
//   };

//   if (itemType.value === 1) {
//     // 房间调价验证
//     rules.marketPrice = [
//       { required: true, message: '请输入市场价', trigger: 'blur' },
//       { type: 'number', min: 0.01, message: '市场价必须大于0', trigger: 'blur' },
//     ];
//   } else if (itemType.value === 2) {
//     // 会场调价验证 - 至少填写一个价格
//     rules.fullDayMarketPrice = [
//       {
//         validator: (rule: any, value: any) => {
//           if (!value && !formModel.value.fullDayMarketPrice) {
//             return Promise.reject('请填写全天市场价');
//           }
//           if (value && value <= 0) {
//             return Promise.reject('全天市场价必须大于0');
//           }
//           return Promise.resolve();
//         },
//         trigger: 'blur',
//       },
//     ];
//     rules.halfDayMarketPrice = [
//       {
//         validator: (rule: any, value: any) => {
//           if (!value && !formModel.value.fullDayMarketPrice) {
//             return Promise.reject('请填写半天市场价');
//           }
//           if (value && value <= 0) {
//             return Promise.reject('半天市场价必须大于0');
//           }
//           return Promise.resolve();
//         },
//         trigger: 'blur',
//       },
//     ];
//   }

//   return rules;
// });

// 自定义文件验证器
const validateFileList = (rule: any, value: any) => {
  if (!fileList.value || fileList.value.length === 0) {
    return Promise.reject('请上传见证性材料');
  }
  return Promise.resolve();
};

onMounted(async () => {
  const codeFromRoute = route.query.code || record?.code;
  const showSubmitFromRoute = route.query.showSubmit;
  showSubmitActions.value = showSubmitFromRoute === '1';

  if (codeFromRoute) {
    priceInquiryCode.value = String(codeFromRoute);
    // console.log('priceInquiryCode', priceInquiryCode.value);
    await fetchPriceInquiryDetail();
  } else {
    // console.log('没有传入code参数，无法获取询价单详情');
  }
});

//获取市场价数据逻辑
const price = ref<IPriceInquiry['resourceHotelRooms'] | null>(null);
const Market = ref<IPriceInquiry['resourceHotelPlaces'] | null>(null);
const pictureList = ref();

// 获取价格询价详情
const fetchPriceInquiryDetail = async () => {
  try {
    loading.value = true;
    if (priceInquiryCode.value) {
      const response = await priceInquiryApi.get({ inquiryCode: priceInquiryCode.value });
      priceInquiryData.value = response;
      // console.log(priceInquiryData, "priceInquiryData");
      pictureList.value = response.resourceHotelPlaces;

      const priceList = await priceInquiryApi.getInquiryDetails({ inquiryCode: priceInquiryCode.value });
      price.value = priceList.resourceHotelRooms;
      Market.value = priceList.resourceHotelPlaces;

      if (response) {
        enableQuarterSetting.value = Boolean(response.enableQuarter);
        resourceHotelQuarters.value = response.resourceHotelQuarters;
        if (resourceHotelQuarters.value.length > 0) {
          enablejudgment.value = true;
        } else {
          enablejudgment.value = false;
        }
        enableStairsSetting.value = Boolean((response as any).enableStairs);
        isLocal.value = response.isLocal;
        console.log(isLocal.value, 'isLocal');
      }
    } else {
      // console.log('priceInquiryCode为空，不调用API');
    }
  } catch (error) {
    // console.error('获取价格询价详情失败:', error);
  } finally {
    loading.value = false;
  }
};

// 在需要过滤季节的地方使用常量
const getLightSeasonQuarters = () => {
  return priceInquiryData.value?.resourceHotelQuarters?.filter((q) => q.season === SeasonTypeConstant.LIGHT.type) || [];
};

const getPeakSeasonQuarters = () => {
  return priceInquiryData.value?.resourceHotelQuarters?.filter((q) => q.season === SeasonTypeConstant.PEAK.type) || [];
};

// 获取床型展示名称
const getRoomTypeName = (roomTypeId?: number) => {
  switch (roomTypeId) {
    case RoomTypeConstant.BIG.code:
      return RoomTypeConstant.BIG.desc;
    case RoomTypeConstant.DOUBLE.code:
      return RoomTypeConstant.DOUBLE.desc;
    case RoomTypeConstant.ROOM.code:
      return RoomTypeConstant.ROOM.desc;
    default:
      return '未知类型';
  }
};

// 处理房间价格数据，根据priceResults构建展示数据
const processedRoomData = computed(() => {
  

  if (!priceInquiryData.value?.resourceHotelRooms || priceInquiryData.value?.resourceHotelRooms.length == 0) {
    return [];
  }
  console.log('改变了:',priceInquiryData.value?.resourceHotelRooms);

  let priceObj: Record<string, any> = []

  priceInquiryData.value.resourceHotelRooms.map((room: any, index) => {
    priceObj.push({
      id: room.id,
      roomType: room.roomType,
      roomName: getRoomTypeName(room.roomType),
      roomDetails: room.roomDetails,
      marketPrice: null,//市场价
      listPrice: null,
      offSeasonPrice: null,
      peakListPrice: null,
      lightSeasonTier: null, //普通协议价
      lightSeasonTier1: null,
      lightSeasonTier2: null,
      peakSeasonTier1: null,
      peakSeasonTier2: null,
      breakfast: '',
      fileInfo: room.fileInfo, // 添加文件信息字段
    });
    // 根据房间类型设置早餐信息
    if (room.roomType === RoomTypeConstant.BIG.code) {
      priceObj[index].breakfast = '单早';
    } else if (room.roomType === RoomTypeConstant.DOUBLE.code) {
      priceObj[index].breakfast = '双早';
    } else if (room.roomType === RoomTypeConstant.ROOM.code) {
      priceObj[index].breakfast = '双早';
    } else {
      priceObj[index].breakfast = '无早';
    }

    // 检查文件信息
    if (room.fileInfo && room.fileInfo.filePath) {
      priceObj[index].fileInfo = {
        filePath: room.fileInfo.filePath,
        fileName: room.fileInfo.fileName || '文件'
      };
    } else if (room.priceResults) {
      // 检查是否在priceResults中有带附件的价格项
      const marketPriceWithFile = room.priceResults.find((item: any) =>
        item.priceItem === PriceTypeConstant.MARKET_PRICE.code &&
        item.type === FileTypeConstant.WITNESS_MATERIALS.code &&
        item.path
      );

      if (marketPriceWithFile) {
        priceObj[index].fileInfo = {
          filePath: marketPriceWithFile.path,
          fileName: marketPriceWithFile.fileName || '文件'
        };
      }
    }

    // 市场价
    const marketPriceItem = room.priceResults?.find(
      (item: any) => item.priceItem === PriceTypeConstant.LIGHT_SEASON_MARKET_PRICE.code,
    );
    if (marketPriceItem) {
      priceObj[index].marketPrice = marketPriceItem.price;
    }

    // 直接查找价格项 - 门市价 (作为淡季门市价)
    const offSeason = room.priceResults?.find(
      (item: any) => item.priceItem === PriceTypeConstant.LIGHT_SEASON_MARKET_PRICE.code,
    );
    if (offSeason) {
      priceObj[index].offSeasonPrice = offSeason.price;
    }

    //普通门市价
    const listPriceItem = room.priceResults?.find((item: any) => item.priceItem === PriceTypeConstant.LIST_PRICE.code);
    if (listPriceItem) {
      priceObj[index].listPrice = listPriceItem.price;
    }
    //普通协议价
    const negotiatedPrice = room.priceResults?.find(
      (item: any) => item.priceItem === PriceTypeConstant.AGREEMENT_PRICE.code,
    );
    if (negotiatedPrice) {
      // console.log('room.priceResults', room.priceResults);
      priceObj[index].lightSeasonTier = negotiatedPrice.price;
    }

    // 设置旺季门市价
    const peakListPriceItem = room.priceResults?.find(
      (item: any) => item.priceItem === PriceTypeConstant.PEAK_SEASON_MARKET_PRICE.code,
    );
    if (peakListPriceItem) {
      priceObj[index].peakListPrice = peakListPriceItem.price;
    }

    // 淡季协议价
    const lightSeasonAgreementUnder50 = room.priceResults?.find(
      (item: any) => item.priceItem === PriceTypeConstant.LIGHT_SEASON_AGREEMENT_PRICE_UNDER_50.code,
    );
    if (lightSeasonAgreementUnder50) {
      priceObj[index].lightSeasonTier1 = lightSeasonAgreementUnder50.price;
    }

    // 淡季协议价,50人以上
    const lightSeasonAgreementAbove50 = room.priceResults?.find(
      (item: any) => item.priceItem === PriceTypeConstant.LIGHT_SEASON_AGREEMENT_PRICE_ABOVE_50.code,
    );
    if (lightSeasonAgreementAbove50) {
      priceObj[index].lightSeasonTier2 = lightSeasonAgreementAbove50.price;
    }

    // 旺季协议价,50人以下
    const peakSeasonAgreementUnder50 = room.priceResults?.find(
      (item: any) => item.priceItem === PriceTypeConstant.PEAK_SEASON_AGREEMENT_PRICE_UNDER_50.code,
    );
    if (peakSeasonAgreementUnder50) {
      priceObj[index].peakSeasonTier1 = peakSeasonAgreementUnder50.price;
    }

    // 旺季协议价,50人以上
    const peakSeasonAgreementAbove50 = room.priceResults?.find(
      (item: any) => item.priceItem === PriceTypeConstant.PEAK_SEASON_AGREEMENT_PRICE_ABOVE_50.code,
    );
    if (peakSeasonAgreementAbove50) {
      priceObj[index].peakSeasonTier2 = peakSeasonAgreementAbove50.price;
    }
  });
  price.value?.map((item: any, index: number) => {
    const marketPriceItem = item.priceResults?.find(
      (item: any) => item.priceItem === PriceTypeConstant.MARKET_PRICE.code,
    );
    if (marketPriceItem) {
      priceObj[index].marketPrice = marketPriceItem.price;
    } else {
      priceObj[index].marketPrice = null;
    }
    if (!priceObj[index].fileInfo?.fileName) {
      
      const marketPriceWithFile = item.priceResults.find(
        (item: any, index: number) =>
          item.priceItem === PriceTypeConstant.MARKET_PRICE.code &&
          item.type === FileTypeConstant.WITNESS_MATERIALS.code &&
          item.path,
      );
      if (marketPriceWithFile) {
        priceObj[index].fileInfo = {
          filePath: marketPriceWithFile.path,
          fileName: marketPriceWithFile.fileName || null,
        };
      }
    }

  });

  return priceObj;
});

// 处理会场价格数据
const processedVenueData = computed(() => {
  if (!(priceInquiryData.value as any)?.resourceHotelPlaces || priceInquiryData.value?.resourceHotelPlaces?.length == 0) {
    return [];
  }
  console.log('改变了:',priceInquiryData.value?.resourceHotelPlaces);
  const venues = (priceInquiryData.value as any).resourceHotelPlaces || [];
  let venueObj: Record<string, any> = [];
  venues.map((venue: any, index: number) => {
    venueObj.push({
      id: venue.id,
      platformPlaceId: venue.platformPlaceId,
      placeName: venue.placeName,
      capacity: venue.maxNum,
      area: venue.area,
      height: venue.height,
      length: venue.length,
      width: venue.wide,
      floor: venue.floor,
      hasColumn: venue.isPillar ? '是' : '否',
      images: venue.images,
      fileInfo: venue.fileInfo,
      tableImage: [],
      fullDayMarketPrice: null,
      halfDayMarketPrice: null,
      lightSeasonFullDayListPrice: null,
      lightSeasonFullDayAgreementPrice: null,
      lightSeasonHalfDayListPrice: null,
      lightSeasonHalfDayAgreementPrice: null,
      peakSeasonFullDayListPrice: null,
      peakSeasonFullDayAgreementPrice: null,
      peakSeasonHalfDayListPrice: null,
      peakSeasonHalfDayAgreementPrice: null,
      Ordinaryhalfdaymarketprice: null,
      OrdinaryRegularfulldaymarketprice: null,
      OrdinaryhalfdayAgreementprice: null,
      OrdinaryRegularfulldayAgreementprice: null,

      // 添加桌型信息
      tableShapes: {},
    });

    // 处理桌型信息
    if (venue.tableResults && Array.isArray(venue.tableResults)) {
      venue.tableResults.forEach((table: any) => {
        venueObj[index].tableShapes[table.tableType] = table.maxNum;
      });
    }

    if (venue.tableResults && Array.isArray(venue.tableResults)) {
      venue.tableResults.forEach((item) => {
        venueObj[index].tableImage.push(item);
      });
    }

    // 检查文件信息
    if (venue.fileInfo && venue.fileInfo.filePath) {
      venueObj[index].fileInfo = {
        filePath: venue.fileInfo.filePath,
        fileName: venue.fileInfo.fileName || '文件'
      };
    } else if (venue.priceResults) {
      // 检查是否在priceResults中有带附件的价格项
      const priceItemWithFile = venue.priceResults.find((item: any) =>
        (item.priceItem === PriceTypeConstant.MARKET_PRICE_FULL_DAY.code ||
          item.priceItem === PriceTypeConstant.MARKET_PRICE_HALF_DAY.code) &&
        item.type === FileTypeConstant.WITNESS_MATERIALS.code &&
        item.path
      );


      if (priceItemWithFile) {
        venueObj[index].fileInfo = {
          filePath: priceItemWithFile.path,
          fileName: priceItemWithFile.fileName || '文件'
        };
      }
    }

    if (venue.priceResults && Array.isArray(venue.priceResults) && venue.priceResults.length > 0) {
      // 全天市场价
      const fullDayMarketPrice = venue.priceResults.find(
        (item: any) => item.priceItem === PriceTypeConstant.MARKET_PRICE_FULL_DAY.code,
      );
      if (fullDayMarketPrice) {
        venueObj[index].fullDayMarketPrice = fullDayMarketPrice.price;
      }
      // 半天市场价
      const halfDayMarketPrice = venue.priceResults.find(
        (item: any) => item.priceItem === PriceTypeConstant.MARKET_PRICE_HALF_DAY.code,
      );
      if (halfDayMarketPrice) {
        venueObj[index].halfDayMarketPrice = halfDayMarketPrice.price;
      }
      // 淡季全天门市价
      const lightSeasonFullDayMarketPrice = venue.priceResults.find(
        (item: any) => item.priceItem === PriceTypeConstant.LIGHT_SEASON_FULL_DAY_MARKET_PRICE.code,
      );
      if (lightSeasonFullDayMarketPrice) {
        venueObj[index].lightSeasonFullDayListPrice = lightSeasonFullDayMarketPrice.price;
      }

      // 淡季全天协议价
      const lightSeasonFullDayAgreementPrice = venue.priceResults.find(
        (item: any) => item.priceItem === PriceTypeConstant.LIGHT_SEASON_FULL_DAY_AGREEMENT_PRICE.code,
      );
      if (lightSeasonFullDayAgreementPrice) {
        venueObj[index].lightSeasonFullDayAgreementPrice = lightSeasonFullDayAgreementPrice.price;
      }

      // 淡季半天门市价
      const lightSeasonHalfDayMarketPrice = venue.priceResults.find(
        (item: any) => item.priceItem === PriceTypeConstant.LIGHT_SEASON_HALF_DAY_MARKET_PRICE.code,
      );
      if (lightSeasonHalfDayMarketPrice) {
        venueObj[index].lightSeasonHalfDayListPrice = lightSeasonHalfDayMarketPrice.price;
      }

      // 淡季半天协议价
      const lightSeasonHalfDayAgreementPrice = venue.priceResults.find(
        (item: any) => item.priceItem === PriceTypeConstant.LIGHT_SEASON_HALF_DAY_AGREEMENT_PRICE.code,
      );
      if (lightSeasonHalfDayAgreementPrice) {
        venueObj[index].lightSeasonHalfDayAgreementPrice = lightSeasonHalfDayAgreementPrice.price;
      }

      // 旺季全天门市价
      const peakSeasonFullDayMarketPrice = venue.priceResults.find(
        (item: any) => item.priceItem === PriceTypeConstant.PEAK_SEASON_FULL_DAY_MARKET_PRICE.code,
      );
      if (peakSeasonFullDayMarketPrice) {
        venueObj[index].peakSeasonFullDayListPrice = peakSeasonFullDayMarketPrice.price;
      }

      // 旺季全天协议价
      const peakSeasonFullDayAgreementPrice = venue.priceResults.find(
        (item: any) => item.priceItem === PriceTypeConstant.PEAK_SEASON_FULL_DAY_AGREEMENT_PRICE.code,
      );
      if (peakSeasonFullDayAgreementPrice) {
        venueObj[index].peakSeasonFullDayAgreementPrice = peakSeasonFullDayAgreementPrice.price;
      }

      // 旺季半天门市价
      const peakSeasonHalfDayMarketPrice = venue.priceResults.find(
        (item: any) => item.priceItem === PriceTypeConstant.PEAK_SEASON_HALF_DAY_MARKET_PRICE.code,
      );
      if (peakSeasonHalfDayMarketPrice) {
        venueObj[index].peakSeasonHalfDayListPrice = peakSeasonHalfDayMarketPrice.price;
      }

      // 旺季半天协议价
      const peakSeasonHalfDayAgreementPrice = venue.priceResults.find(
        (item: any) => item.priceItem === PriceTypeConstant.PEAK_SEASON_HALF_DAY_AGREEMENT_PRICE.code,
      );
      if (peakSeasonHalfDayAgreementPrice) {
        venueObj[index].peakSeasonHalfDayAgreementPrice = peakSeasonHalfDayAgreementPrice.price;
      }

      //普通半天门市价
      const Ordinaryhalfdaymarketprice = venue.priceResults.find(
        (item: any) => item.priceItem === PriceTypeConstant.LIST_PRICE_FULL_DAY.code,
      );
      if (Ordinaryhalfdaymarketprice) {
        venueObj[index].Ordinaryhalfdaymarketprice = Ordinaryhalfdaymarketprice.price;
      }
      //普通全天门市价
      const OrdinaryRegularfulldaymarketprice = venue.priceResults.find(
        (item: any) => item.priceItem === PriceTypeConstant.LIST_PRICE_HALF_DAY.code,
      );
      if (OrdinaryRegularfulldaymarketprice) {
        venueObj[index].OrdinaryRegularfulldaymarketprice = OrdinaryRegularfulldaymarketprice.price;
      }
      //普通半天协议价
      const OrdinaryhalfdayAgreementprice = venue.priceResults.find(
        (item: any) => item.priceItem === PriceTypeConstant.AGREEMENT_PRICE_FULL_DAY.code,
      );
      if (OrdinaryhalfdayAgreementprice) {
        venueObj[index].OrdinaryhalfdayAgreementprice = OrdinaryhalfdayAgreementprice.price;
      }
      //普通半天协议价
      const OrdinaryRegularfulldayAgreementprice = venue.priceResults.find(
        (item: any) => item.priceItem === PriceTypeConstant.AGREEMENT_PRICE_HALF_DAY.code,
      );
      if (OrdinaryRegularfulldayAgreementprice) {
        venueObj[index].OrdinaryRegularfulldayAgreementprice = OrdinaryRegularfulldayAgreementprice.price;
      }
    }
  });
  Market.value?.map((item: any, index: number) => {
    const fullDayMarketPrice = item.priceResults?.find(
      (item: any) => item.priceItem === PriceTypeConstant.MARKET_PRICE_FULL_DAY.code,
    );
    if (fullDayMarketPrice) {
      venueObj[index].fullDayMarketPrice = fullDayMarketPrice.price;
    }
    // 半天市场价
    const halfDayMarketPrice = item.priceResults.find(
      (item: any) => item.priceItem === PriceTypeConstant.MARKET_PRICE_HALF_DAY.code,
    );
    if (halfDayMarketPrice) {
      venueObj[index].halfDayMarketPrice = halfDayMarketPrice.price;
    }
    if (!venueObj[index].fileInfo?.fileName) {
      const marketPriceWithFile = item.priceResults.find(
        (item: any, index: number) =>
          item.priceItem === PriceTypeConstant.MARKET_PRICE_FULL_DAY.code &&
          item.type === FileTypeConstant.WITNESS_MATERIALS.code &&
          item.path,
      );
      if (marketPriceWithFile) {
        venueObj[index].fileInfo = {
          filePath: marketPriceWithFile.path,
          fileName: marketPriceWithFile.fileName || '文件',
        };
      }
    }

  });
  return venueObj;
});

const priceDetermination = (res: string) => {
  if (!price.value) return 0.00; // 防御性处理
  const room = price.value.find((item) => item.roomName === res);
  return room?.priceResults?.find((item) => item.priceItem === 10)?.price.toFixed(2) ?? 0.00;
};

const venueJudgment = (res: string) => {
  if (!Market.value?.length) return 0.00;
  const room = Market.value.find((item) => item.placeName === res);
  const allDay = room?.priceResults?.find((item) => item.priceItem === 180)?.price.toFixed(2) ?? 0.00;
  return allDay;
};

const halfDayJudgment = (res: string) => {
  if (!Market.value?.length) return 0.00;
  const room = Market.value.find((item) => item.placeName === res);
  const halfDay = room?.priceResults?.find((item) => item.priceItem === 190)?.price.toFixed(2) ?? 0.00;
  return halfDay;
};

// 住宿表格列定义
const tableColumns = computed(() => {
  // 基础列 - 床型始终显示
  const baseColumns = [
    {
      title: '床型',
      dataIndex: 'roomName',
      align: 'center' as const,
      key: 'roomName',
    },
  ];

  // 市场价列定义
  const marketPriceColumn = {
    title: '市场价',
    dataIndex: 'marketPrice',
    align: 'center' as const,
    key: 'marketPrice',
    customRender: ({ text, record }: { text: any; record: any }) => {
      if (text === undefined || text === null) {
        // console.log('为空');
        return priceDetermination(record.roomName).toFixed(2);
      } else {
        // 判断市场价是否大于所有协议价
        const isGreaterThanAllAgreementPrices = () => {
          // console.log('执行力');

          // 获取所有协议价
          const agreementPrices = [];
          const priceDetails = [];

          // 协议价
          if (record.lightSeasonTier !== null && record.lightSeasonTier1 == undefined) {
            agreementPrices.push(record.lightSeasonTier);
            if (text <= record.lightSeasonTier) {
              priceDetails.push(`协议价: ${record.lightSeasonTier}元`);
            }
          }

          // 淡季协议价(50人及以下)
          if (record.lightSeasonTier1 !== null && record.lightSeasonTier1 !== undefined) {
            agreementPrices.push(record.lightSeasonTier1);
            if (text <= record.lightSeasonTier1) {
              priceDetails.push(`淡季协议价: ${record.lightSeasonTier1}元`);
            }
          }

          // 淡季协议价(50人以上)
          if (record.lightSeasonTier2 !== null && record.lightSeasonTier2 !== undefined) {
            agreementPrices.push(record.lightSeasonTier2);
            if (text <= record.lightSeasonTier2) {
              priceDetails.push(`淡季协议价: ${record.lightSeasonTier2}元`);
            }
          }

          // 旺季协议价(50人及以下)
          if (record.peakSeasonTier1 !== null && record.peakSeasonTier1 !== undefined) {
            agreementPrices.push(record.peakSeasonTier1);
            if (text <= record.peakSeasonTier1) {
              priceDetails.push(`旺季协议价: ${record.peakSeasonTier1}元`);
            }
          }

          // 旺季协议价(50人以上)
          if (record.peakSeasonTier2 !== null && record.peakSeasonTier2 !== undefined) {
            agreementPrices.push(record.peakSeasonTier2);
            if (text <= record.peakSeasonTier2) {
              priceDetails.push(`旺季协议价: ${record.peakSeasonTier2}元`);
            }
          }

          // 如果没有协议价，返回true
          if (agreementPrices.length === 0) {
            return { isGreater: true, priceDetails: [] };
          }

          // 判断市场价是否大于所有协议价

          return {
            isGreater: agreementPrices.every((price) => text > price),
            priceDetails: priceDetails,
          };
        };

        const { isGreater, priceDetails } = isGreaterThanAllAgreementPrices();

        // 创建提示内容
        const tooltipContent =
          priceDetails.length > 0
            ? h('div', {}, [
              h('p', { style: { margin: '0', fontWeight: 'bold' } }, '市场价不能低于以下协议价:'),
              ...priceDetails.map((detail) => h('p', { style: { margin: '4px 0' } }, detail)),
            ])
            : '';

        return h('div', { class: 'market-price-cell' }, [
          h('span', { class: 'market-price-value' }, text.toFixed(2) + '元'),
          isGreater
            ? h(CheckCircleFilled, { style: { color: '#52c41a', fontSize: '16px', marginLeft: '8px' } })
            : h(
              hTooltip,
              {
                title: tooltipContent,
                color: '#ff4d4f',
              },
              () =>
                h(CloseCircleFilled, {
                  style: { color: '#f5222d', fontSize: '16px', marginLeft: '8px', cursor: 'pointer' },
                }),
            ),
        ]);
      }
    },
  };

  // 文件列定义
  const fileColumn = {
    title: '见证性材料',
    dataIndex: 'fileInfo',
    align: 'center' as const,
    width: '150px',
    key: 'fileInfo',
    customRender: ({ record }: { record: any }) => {
      if (!record.fileInfo) {
        // console.log(record,"record");

        return '无';
      } else {
        return h(
          'a',
          {
            href: record.fileInfo.filePath,
            target: '_blank',
            style: { color: '#1890ff' },
          },
          '查看文件',
        );
      }
    },
  };
  //判断是否有淡旺季时间
  if (enablejudgment.value) {
    const columns = [
      ...baseColumns,
      marketPriceColumn,
      {
        title: '淡季门市价',
        dataIndex: 'offSeasonPrice',
        align: 'center' as const,
        key: 'offSeasonPrice',
        customRender: ({ text }: { text: any }) => {
          if (text === undefined || text === null) {
            return '暂无数据';
          }
          return text.toFixed(2) + '元';
        },
      },
      {
        title: '淡季协议价',
        dataIndex: 'lightSeasonTier1',
        align: 'center' as const,
        key: 'lightSeasonTier1',
        customRender: ({ text }: { text: any }) => {
          if (text === undefined || text === null) {
            return '暂无数据';
          }
          return text.toFixed(2) + '元';
        },
      },
      {
        title: '旺季门市价',
        dataIndex: 'peakListPrice',
        align: 'center' as const,
        key: 'peakListPrice',
        customRender: ({ text }: { text: any }) => {
          if (text === undefined || text === null) {
            return '暂无数据';
          }
          return text.toFixed(2) + '元';
        },
      },
      {
        title: '旺季协议价',
        dataIndex: 'peakSeasonTier1',
        align: 'center' as const,
        key: 'peakSeasonTier1',
        customRender: ({ text }: { text: any }) => {
          if (text === undefined || text === null) {
            return '暂无数据';
          }
          return text.toFixed(2) + '元';
        },
      },
      fileColumn, // 添加文件列
      { title: '早餐提供', dataIndex: 'breakfast', align: 'center' as const, key: 'breakfast' },
    ];

    // 仅在有提交权限时添加操作列
    if (showSubmitActions.value) {
      columns.push({
        title: '操作',
        key: 'operation',
        dataIndex: 'id',
        align: 'center' as const,
      });
    }

    return columns;
  } else {
    // 如果enableQuarterSetting为false，只显示简化版的价格列
    const columns = [
      ...baseColumns,
      marketPriceColumn,
      {
        title: '门市价',
        dataIndex: 'listPrice',
        align: 'center' as const,
        key: 'listPrice',
        customRender: ({ text }: { text: any }) => {
          if (text === undefined || text === null) {
            return '暂无数据';
          }
          return text.toFixed(2) + '元';
        },
      },
      {
        title: '协议价',
        dataIndex: 'lightSeasonTier',
        align: 'center' as const,
        key: 'lightSeasonTier',
        customRender: ({ text }: { text: any }) => {
          if (text === undefined || text === null) {
            return '暂无数据';
          }
          return text.toFixed(2) + '元';
        },
      },
      fileColumn, // 添加文件列
      { title: '早餐提供', dataIndex: 'breakfast', align: 'center' as const, key: 'breakfast' },
    ];

    // 仅在有提交权限时添加操作列
    if (showSubmitActions.value) {
      columns.push({
        title: '操作',
        key: 'operation',
        dataIndex: 'id',
        align: 'center' as const,
      });
    }

    return columns;
  }
});

let isShowTitle = false;
let imgTitle = '';

const modifyPicture = (img: { src: string; title: number }) => {
  previewImage.value = img.src;
  modifyTitle(img.title);
};

const modifyTitle = (num: number) => {
  switch (num) {
    case 1: // U型式
      imgTitle = 'U型式';
      break;
    case 2: // 宴会会式/董事会式
      imgTitle = '宴会式/董事会式';
      break;
    case 3: // 剧院式
      imgTitle = '剧院式';
      break;
    case 4: // 海岛式
      imgTitle = '海岛式';
      break;
    case 5: // 鸡尾酒/酒会式
      imgTitle = '鸡尾酒/酒会式';
      break;
    case 6: // 课桌式
      imgTitle = '课桌式';
      break;
    case 7: // 鱼骨式
      imgTitle = '鱼骨式';
      break;
  }
};

// 图片表格列定义
const customRender = ({ record }: { record: any }) => {
  // console.log(record,"record");

  if (record.images && record.images.length > 0) {
    // 在表格中只显示第一张图片
    return h(
      'div',
      {
        style: {
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        },
      },
      [
        // 显示第一张图片
        h('img', {
          src: record.images[0].path,
          style: {
            width: '80px',
            height: '60px',
            objectFit: 'cover',
            border: '1px solid #eee',
            borderRadius: '2px',
            cursor: 'pointer',
          },
          onClick: () => {
            isShowTitle = false;
            // 点击放大查看所有图片
            previewImage.value = record.images[0].path;
            previewImages.value = record.images.map((url) => ({
              src: url.path,
              title: url.path || '',
            }));
            previewVisible.value = true;
          },
        }),
        // 如果有多张图片，显示 +n张 的文本
        record.images.length > 1
          ? h(
            'div',
            {
              style: {
                fontSize: '12px',
                color: '#1890ff',
                cursor: 'pointer',
                marginTop: '4px',
              },
              onClick: () => {
                // 点击查看全部图片
                isShowTitle = false;
                previewImage.value = record.images[0].path;
                previewImages.value = record.images.map((url) => ({
                  src: url.path,
                  title: record.venueName || '',
                }));
                previewVisible.value = true;
              },
            },
            `+${record.images.length - 1}张`,
          )
          : null,
      ],
    );
  }
  return '-';
};

const tableCustomRender = ({ record }: { record: any }) => {
  // console.log(record.tableImage, "record.tableImage");
  const tableImage: any[] = [];
  record.tableImage.forEach((item: { tableUrls: string | any[] }) => {
    if (item.tableUrls.length > 0) {
      tableImage.push(item);
    }
  });

  if (tableImage && tableImage.length > 0) {
    // 在表格中只显示第一张图片
    return h(
      'div',
      {
        style: {
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        },
      },
      [
        // 显示第一张图片
        h('img', {
          src: tableImage[0].tableUrls[0]?.path,
          style: {
            width: '80px',
            height: '60px',
            objectFit: 'cover',
            border: '1px solid #eee',
            borderRadius: '2px',
            cursor: 'pointer',
          },
          onClick: () => {
            // 点击放大查看所有图片
            isShowTitle = true;
            previewImage.value = tableImage[0].tableUrls[0]?.path;
            modifyTitle(tableImage[0]?.tableType);
            previewImages.value = tableImage.map((url) => ({
              src: url.tableUrls[0]?.path,
              title: url.tableType || '',
            }));
            previewVisible.value = true;
          },
        }),
        // 如果有多张图片，显示 +n张 的文本
        tableImage.length > 1
          ? h(
            'div',
            {
              style: {
                fontSize: '12px',
                color: '#1890ff',
                cursor: 'pointer',
                marginTop: '4px',
              },
              onClick: () => {
                // 点击查看全部图片
                isShowTitle = true;
                previewImage.value = tableImage[0]?.tableUrls[0]?.path;
                modifyTitle(tableImage[0]?.tableType);
                previewImages.value = tableImage.map((url) => ({
                  src: url.tableUrls[0]?.path,
                  title: url.tableType || '',
                }));
                previewVisible.value = true;
              },
            },
            `+${tableImage.length - 1}张`,
          )
          : null,
      ],
    );
  }
  return '-';
};

// 会场表格列定义
const venueTableColumns = computed(() => {
  // 基础列 - 始终显示
  const baseColumns = [
    { title: '会场名称', dataIndex: 'placeName', align: 'center' as const, width: '150px', key: 'placeName' },
    {
      title: '会场图片',
      dataIndex: 'images',
      align: 'center' as const,
      width: '100px',
      key: 'images',
      customRender: customRender,
    },
    {
      title: '桌型图片',
      dataIndex: 'tableImage',
      align: 'center' as const,
      width: '120px',
      key: 'tableImage',
      customRender: tableCustomRender,
    },
    {
      title: '最大可容纳人数',
      dataIndex: 'capacity',
      align: 'center' as const,
      width: '180px',
      key: 'capacity',
      customRender: ({ text, record }: { text: any; record: any }) => {
        // 构建桌型容量提示信息
        const tooltipItems = [];

        for (const key in TableShapeConstant) {
          const tableType = TableShapeConstant[key as any].type;
          if (record.tableShapes && record.tableShapes[tableType] !== undefined) {
            tooltipItems.push(
              h('div', {}, `${TableShapeConstant[key as any].name}：${record.tableShapes[tableType]}人`),
            );
          }
        }

        return h('div', { style: { display: 'flex', alignItems: 'center', justifyContent: 'center' } }, [
          h('span', {}, text),
          h(
            hTooltip,
            {
              title: h('div', {}, tooltipItems.length > 0 ? tooltipItems : [h('div', {}, '暂无详细数据')]),
            },
            () => h(QuestionCircleFilled, { style: { color: '#1890ff', fontSize: '16px', marginLeft: '8px' } }),
          ),
        ]);
      },
    },
    {
      title: '面积(㎡)', dataIndex: 'area', align: 'center' as const, width: '120px', key: 'area', customRender: ({ text }: { text: any }) => {
        return text ? `${text.toFixed(2)}` : '暂无数据';
      }
    },
    {
      title: '层高(m)', dataIndex: 'height', align: 'center' as const, width: '120px', key: 'height', customRender: ({ text }: { text: any }) => {
        return text ? `${text.toFixed(2)}` : '暂无数据';
      }
    },
    {
      title: '长(m)', dataIndex: 'length', align: 'center' as const, width: '120px', key: 'length', customRender: ({ text }: { text: any }) => {
        return text ? `${text.toFixed(2)}` : '暂无数据';
      }
    },
    {
      title: '宽(m)', dataIndex: 'width', align: 'center' as const, width: '120px', key: 'width', customRender: ({ text }: { text: any }) => {
        return text ? `${text.toFixed(2)}` : '暂无数据';
      }
    },
    { title: '楼层', dataIndex: 'floor', align: 'center' as const, width: '120px', key: 'floor' },
    { title: '是否有柱', dataIndex: 'hasColumn', align: 'center' as const, width: '150px', key: 'hasColumn' },
  ];

  // 文件列定义
  const fileColumn = {
    title: '见证性材料',
    dataIndex: 'fileInfo',
    align: 'center' as const,
    width: '150px',
    key: 'fileInfo',
    customRender: ({ record }: { record: any }) => {
      if (!record.fileInfo) {
        // console.log(record,"record");

        return '无';
      } else {
        return h(
          'a',
          {
            href: record.fileInfo.filePath,
            target: '_blank',
            style: { color: '#1890ff' },
          },
          '查看文件',
        );
      }
    },
  };

  // 全天市场价列定义
  const fullDayMarketPriceColumn = {
    title: '全天市场价',
    dataIndex: 'fullDayMarketPrice',
    align: 'center' as const,
    width: '180px',
    key: 'fullDayMarketPrice',
    customRender: ({ text, record }: { text: any; record: any }) => {
      if (text === undefined || text === null) {
        return venueJudgment(record.placeName).toFixed(2);
      } else {
        // 判断全天市场价是否大于所有全天协议价
        const isGreaterThanAllAgreementPrices = () => {
          // 获取所有全天协议价
          const agreementPrices = [];
          const priceDetails = [];

          // 普通全天协议价
          if (
            record.OrdinaryRegularfulldayAgreementprice !== null &&
            record.OrdinaryRegularfulldayAgreementprice !== undefined
          ) {
            agreementPrices.push(record.OrdinaryRegularfulldayAgreementprice);
            if (text <= record.OrdinaryRegularfulldayAgreementprice) {
              priceDetails.push(`全天协议价: ${record.OrdinaryRegularfulldayAgreementprice}元`);
            }
          }

          // 旺季全天协议价
          if (record.peakSeasonFullDayAgreementPrice !== null && record.peakSeasonFullDayAgreementPrice !== undefined) {
            agreementPrices.push(record.peakSeasonFullDayAgreementPrice);
            if (text <= record.peakSeasonFullDayAgreementPrice) {
              priceDetails.push(`旺季全天协议价: ${record.peakSeasonFullDayAgreementPrice}元`);
            }
          }

          // 淡季全天协议价
          if (
            record.lightSeasonFullDayAgreementPrice !== null &&
            record.lightSeasonFullDayAgreementPrice !== undefined
          ) {
            agreementPrices.push(record.lightSeasonFullDayAgreementPrice);
            if (text <= record.lightSeasonFullDayAgreementPrice) {
              priceDetails.push(`淡季全天协议价: ${record.lightSeasonFullDayAgreementPrice}元`);
            }
          }

          // 如果没有协议价，返回true
          if (agreementPrices.length === 0) {
            return { isGreater: true, priceDetails: [] };
          }

          // 判断市场价是否大于所有协议价
          return {
            isGreater: agreementPrices.every((price) => text > price),
            priceDetails: priceDetails,
          };
        };

        const { isGreater, priceDetails } = isGreaterThanAllAgreementPrices();

        // 创建提示内容
        const tooltipContent =
          priceDetails.length > 0
            ? h('div', {}, [
              h('p', { style: { margin: '0', fontWeight: 'bold' } }, '市场价不能低于以下协议价:'),
              ...priceDetails.map((detail) => h('p', { style: { margin: '4px 0' } }, detail)),
            ])
            : '';

        return h('div', { class: 'market-price-cell' }, [
          h('span', { class: 'market-price-value' }, text.toFixed(2) + '元'),
          isGreater
            ? h(CheckCircleFilled, { style: { color: '#52c41a', fontSize: '16px', marginLeft: '8px' } })
            : h(
              hTooltip,
              {
                title: tooltipContent,
                color: '#ff4d4f',
              },
              () =>
                h(CloseCircleFilled, {
                  style: { color: '#f5222d', fontSize: '16px', marginLeft: '8px', cursor: 'pointer' },
                }),
            ),
        ]);
      }
    },
  };

  // 半天市场价列定义
  const halfDayMarketPriceColumn = {
    title: '半天市场价',
    dataIndex: 'halfDayMarketPrice',
    align: 'center' as const,
    width: '180px',
    key: 'halfDayMarketPrice',
    customRender: ({ text, record }: { text: any; record: any }) => {
      if (text === undefined || text === null) {
        return halfDayJudgment(record.placeName).toFixed(2);
      } else {
        // 判断半天市场价是否大于所有半天协议价
        const isGreaterThanAllAgreementPrices = () => {
          // 获取所有半天协议价
          const agreementPrices = [];
          const priceDetails = [];

          // 普通半天协议价
          if (record.OrdinaryhalfdayAgreementprice !== null && record.OrdinaryhalfdayAgreementprice !== undefined) {
            agreementPrices.push(record.OrdinaryhalfdayAgreementprice);
            if (text <= record.OrdinaryhalfdayAgreementprice) {
              priceDetails.push(`半天协议价: ${record.OrdinaryhalfdayAgreementprice}元`);
            }
          }

          // 淡季半天协议价
          if (
            record.lightSeasonHalfDayAgreementPrice !== null &&
            record.lightSeasonHalfDayAgreementPrice !== undefined
          ) {
            agreementPrices.push(record.lightSeasonHalfDayAgreementPrice);
            if (text <= record.lightSeasonHalfDayAgreementPrice) {
              priceDetails.push(`淡季半天协议价: ${record.lightSeasonHalfDayAgreementPrice}元`);
            }
          }

          // 旺季半天协议价
          if (record.peakSeasonHalfDayAgreementPrice !== null && record.peakSeasonHalfDayAgreementPrice !== undefined) {
            agreementPrices.push(record.peakSeasonHalfDayAgreementPrice);
            if (text <= record.peakSeasonHalfDayAgreementPrice) {
              priceDetails.push(`旺季半天协议价: ${record.peakSeasonHalfDayAgreementPrice}元`);
            }
          }

          // 如果没有协议价，返回true
          if (agreementPrices.length === 0) {
            return { isGreater: true, priceDetails: [] };
          }

          // 判断市场价是否大于所有协议价
          return {
            isGreater: agreementPrices.every((price) => text > price),
            priceDetails: priceDetails,
          };
        };

        const { isGreater, priceDetails } = isGreaterThanAllAgreementPrices();

        // 创建提示内容
        const tooltipContent =
          priceDetails.length > 0
            ? h('div', {}, [
              h('p', { style: { margin: '0', fontWeight: 'bold' } }, '市场价不能低于以下协议价:'),
              ...priceDetails.map((detail) => h('p', { style: { margin: '4px 0' } }, detail)),
            ])
            : '';

        return h('div', { class: 'market-price-cell' }, [
          h('span', { class: 'market-price-value' }, text.toFixed(2) + '元'),
          isGreater
            ? h(CheckCircleFilled, { style: { color: '#52c41a', fontSize: '16px', marginLeft: '8px' } })
            : h(
              hTooltip,
              {
                title: tooltipContent,
                color: '#ff4d4f',
              },
              () =>
                h(CloseCircleFilled, {
                  style: { color: '#f5222d', fontSize: '16px', marginLeft: '8px', cursor: 'pointer' },
                }),
            ),
        ]);
      }
    },
  };

  // 如果enableQuarterSetting为true，显示完整的淡旺季价格列
  if (enablejudgment.value && isLocal.value) {
    const columns = [
      ...baseColumns,
      fullDayMarketPriceColumn,
      halfDayMarketPriceColumn,
      {
        title: '淡季全天门市价',
        dataIndex: 'lightSeasonFullDayListPrice',
        align: 'center' as const,
        width: '180px',
        key: 'lightSeasonFullDayListPrice',
        customRender: ({ text }: { text: any }) => {
          if (text === undefined || text === null) {
            return '暂无数据';
          }
          return text.toFixed(2) + '元';
        },
      },
      {
        title: '淡季全天协议价',
        dataIndex: 'lightSeasonFullDayAgreementPrice',
        align: 'center' as const,
        width: '180px',
        key: 'lightSeasonFullDayAgreementPrice',
        customRender: ({ text }: { text: any }) => {
          if (text === undefined || text === null) {
            return '暂无数据';
          }
          return text.toFixed(2) + '元';
        },
      },
      {
        title: '淡季半天门市价',
        dataIndex: 'lightSeasonHalfDayListPrice',
        align: 'center' as const,
        width: '180px',
        key: 'lightSeasonHalfDayListPrice',
        customRender: ({ text }: { text: any }) => {
          if (text === undefined || text === null) {
            return '暂无数据';
          }
          return text.toFixed(2) + '元';
        },
      },
      {
        title: '淡季半天协议价',
        dataIndex: 'lightSeasonHalfDayAgreementPrice',
        align: 'center' as const,
        width: '180px',
        key: 'lightSeasonHalfDayAgreementPrice',
        customRender: ({ text }: { text: any }) => {
          if (text === undefined || text === null) {
            return '暂无数据';
          }
          return text.toFixed(2) + '元';
        },
      },
      {
        title: '旺季全天门市价',
        dataIndex: 'peakSeasonFullDayListPrice',
        align: 'center' as const,
        width: '180px',
        key: 'peakSeasonFullDayListPrice',
        customRender: ({ text }: { text: any }) => {
          if (text === undefined || text === null) {
            return '暂无数据';
          }
          return text.toFixed(2) + '元';
        },
      },
      {
        title: '旺季全天协议价',
        dataIndex: 'peakSeasonFullDayAgreementPrice',
        align: 'center' as const,
        width: '180px',
        key: 'peakSeasonFullDayAgreementPrice',
        customRender: ({ text }: { text: any }) => {
          if (text === undefined || text === null) {
            return '暂无数据';
          }
          return text.toFixed(2) + '元';
        },
      },
      {
        title: '旺季半天门市价',
        dataIndex: 'peakSeasonHalfDayListPrice',
        align: 'center' as const,
        width: '180px',
        key: 'peakSeasonHalfDayListPrice',
        customRender: ({ text }: { text: any }) => {
          if (text === undefined || text === null) {
            return '暂无数据';
          }
          return text.toFixed(2) + '元';
        },
      },
      {
        title: '旺季半天协议价',
        dataIndex: 'peakSeasonHalfDayAgreementPrice',
        align: 'center' as const,
        width: '180px',
        key: 'peakSeasonHalfDayAgreementPrice',
        customRender: ({ text }: { text: any }) => {
          if (text === undefined || text === null) {
            return '暂无数据';
          }
          return text.toFixed(2) + '元';
        },
      },
      fileColumn, // 添加文件列
    ];

    // 仅在有提交权限时添加操作列
    if (showSubmitActions.value) {
      columns.push({
        title: '操作',
        key: 'operation',
        dataIndex: 'id',
        align: 'center' as const,
        width: '120px',
      });
    }

    return columns;
  } else if (!enablejudgment.value && isLocal.value) {
    // 如果enableQuarterSetting为false，只显示简化版的价格列
    const columns = [
      ...baseColumns,
      fullDayMarketPriceColumn,
      halfDayMarketPriceColumn,
      {
        title: '全天门市价',
        dataIndex: 'OrdinaryRegularfulldaymarketprice',
        align: 'center' as const,
        width: '140px',
        key: 'OrdinaryRegularfulldaymarketprice',
        customRender: ({ text }: { text: any }) => {
          if (text === undefined || text === null) {
            return '暂无数据';
          }
          return text.toFixed(2) + '元';
        },
      },
      {
        title: '全天协议价',
        dataIndex: 'OrdinaryRegularfulldayAgreementprice',
        align: 'center' as const,
        width: '140px',
        key: 'OrdinaryRegularfulldayAgreementprice',
        customRender: ({ text }: { text: any }) => {
          if (text === undefined || text === null) {
            return '暂无数据';
          }
          return text.toFixed(2) + '元';
        },
      },
      {
        title: '半天门市价',
        dataIndex: 'Ordinaryhalfdaymarketprice',
        align: 'center' as const,
        width: '140px',
        key: 'Ordinaryhalfdaymarketprice',
        customRender: ({ text }: { text: any }) => {
          if (text === undefined || text === null) {
            return '暂无数据';
          }
          return text.toFixed(2) + '元';
        },
      },
      {
        title: '半天协议价',
        dataIndex: 'OrdinaryhalfdayAgreementprice',
        align: 'center' as const,
        width: '140px',
        key: 'OrdinaryhalfdayAgreementprice',
        customRender: ({ text }: { text: any }) => {
          if (text === undefined || text === null) {
            return '暂无数据';
          }
          return text.toFixed(2) + '元';
        },
      },
      fileColumn,
    ];

    // 仅在有提交权限时添加操作列
    if (showSubmitActions.value) {
      columns.push({
        title: '操作',
        key: 'operation',
        dataIndex: 'id',
        align: 'center' as const,
        width: '120px',
      });
    }

    return columns;
  } else if (!isLocal.value) {
    const columns = [...baseColumns];
    return columns;
  }
});

// 处理房间手动调价弹窗
const handleManualAdjust = (processedRoom: any, originalRoomData: any) => {
  const rawData = priceInquiryData.value?.resourceHotelRooms?.find((room: any) => room.id === processedRoom.id);
  const newRawData = price.value?.find((room: any) => processedRoom.id == room.id || processedRoom.roomName.includes(room.roomName) );

  currentItem.value = processedRoom;
  originalItem.value = newRawData;

  marketPrice.value = processedRoom.marketPrice;
  fullDayMarketPrice.value = null;
  halfDayMarketPrice.value = null;
  itemType.value = 1;

  // 重置文件列表
  fileList.value = [];

  // 检查原始数据中是否存在文件信息，如果有则回显
  if (newRawData?.fileInfo && newRawData.fileInfo.filePath) {
    console.log(newRawData, "newRawData");

    // 如果直接保存在对象上的fileInfo属性中
    fileList.value = [
      {
        uid: '-1',
        name: newRawData.fileInfo.fileName || '文件',
        status: 'done',
        url: newRawData.fileInfo.filePath,
        filePath: newRawData.fileInfo.filePath,
        fileName: newRawData.fileInfo.fileName,
      },
    ];
  } else if (newRawData?.priceResults) {
    // 检查是否在priceResults中有带附件的价格项
    const marketPriceWithFile = newRawData?.priceResults.find(
      (item: any) =>
        item.priceItem === PriceTypeConstant.MARKET_PRICE.code &&
        item.type === FileTypeConstant.WITNESS_MATERIALS.code &&
        item.path,
    );
    console.log(marketPriceWithFile, 'marketPriceWithFile');

    if (marketPriceWithFile) {
      fileList.value = [
        {
          uid: '-1',
          name: marketPriceWithFile.path.split('/').pop() || '文件',
          status: 'done',
          url: marketPriceWithFile.path,
          filePath: marketPriceWithFile.path,
          fileName: marketPriceWithFile.path || '文件',
        },
      ];
    }
  } else if (currentItem.value.fileInfo) {
    fileList.value = [
      {
        uid: '-1',
        name: currentItem.value.fileInfo.filePath.split('/').pop() || '文件',
        status: 'done',
        url: currentItem.value.fileInfo.filePath,
        filePath: currentItem.value.fileInfo.filePath,
        fileName: currentItem.value.fileInfo.filePath || '文件',
      },
    ];
  }

  // 初始化表单数据（在文件列表设置完成后）
  formModel.value = {
    marketPrice: processedRoom.marketPrice,
    fullDayMarketPrice: null,
    halfDayMarketPrice: null,
    fileList: [...fileList.value], // 同步文件列表到表单模型
  };

  manualAdjustVisible.value = true;
};

// 处理会场手动调价弹窗
const handleVenueManualAdjust = (venue: any) => {
  const rawData = (priceInquiryData.value as any)?.resourceHotelPlaces?.find((v: any) => v.id === venue.id);
  console.log(Market.value, "Market.value");
  console.log(venue, "Market.value");

  const newRawData = Market.value?.find((v: any) => v.id == venue.id || v.placeName == venue.placeName);
  console.log(newRawData, "newRawData");


  currentItem.value = venue;
  originalItem.value = newRawData;
  marketPrice.value = null;
  fullDayMarketPrice.value = venue.fullDayMarketPrice;
  halfDayMarketPrice.value = venue.halfDayMarketPrice;
  itemType.value = 2; // 会场

  // 重置文件列表
  fileList.value = [];

  // 检查原始数据中是否存在文件信息，如果有则回显
  if (newRawData?.fileInfo && newRawData.fileInfo.filePath) {
    // 如果直接保存在对象上的fileInfo属性中
    fileList.value = [
      {
        uid: '-1',
        name: newRawData.fileInfo.fileName || '文件',
        status: 'done',
        url: newRawData.fileInfo.filePath,
        filePath: newRawData.fileInfo.filePath,
        fileName: newRawData.fileInfo.fileName,
      },
    ];
  } else if (newRawData?.priceResults) {
    // 检查是否在priceResults中有带附件的价格项
    const priceItemWithFile = newRawData.priceResults.find(
      (item: any) =>
        (item.priceItem === PriceTypeConstant.MARKET_PRICE_FULL_DAY.code ||
          item.priceItem === PriceTypeConstant.MARKET_PRICE_HALF_DAY.code) &&
        item.type === FileTypeConstant.WITNESS_MATERIALS.code &&
        item.path,
    );
    console.log(priceItemWithFile, 'priceItemWithFile');

    if (priceItemWithFile) {
      fileList.value = [
        {
          uid: '-1',
          name: priceItemWithFile.path.split('/').pop() || '文件',
          status: 'done',
          url: priceItemWithFile.path,
          filePath: priceItemWithFile.path,
          fileName: priceItemWithFile.path || '文件',
        },
      ];
    }
  }

  // 初始化表单数据（在文件列表设置完成后）
  formModel.value = {
    marketPrice: null,
    fullDayMarketPrice: venue.fullDayMarketPrice,
    halfDayMarketPrice: venue.halfDayMarketPrice,
    fileList: [...fileList.value], // 同步文件列表到表单模型
  };

  manualAdjustVisible.value = true;
};

// 处理价格输入变更
const handlePriceChange = (value: number) => {
  marketPrice.value = value;
  formModel.value.marketPrice = value;
};

// 处理会场价格输入变更
const handleFullDayPriceChange = (value: number) => {
  fullDayMarketPrice.value = value;
  formModel.value.fullDayMarketPrice = value;
};

const handleHalfDayPriceChange = (value: number) => {
  halfDayMarketPrice.value = value;
  formModel.value.halfDayMarketPrice = value;
};

// 上传请求
// @ts-ignore
const baseUrl = import.meta.env.VITE_BUSINESS_URL;
const uploadRequest = (options: any) => {
  uploadLoading.value = true;

  const formData = new FormData();
  formData.append('file', options.file);

  fileApi
    .upload(formData)
    .then((it) => {
      options.file.filePath = baseUrl + it.path;
      options.file.fileName = options.file.name;
      options.onProgress(100);
      options.onSuccess(it, options.file);

      // 更新文件列表
      fileList.value = [options.file];

      // 更新表单模型
      formModel.value.fileList = [options.file];

      // 触发文件字段的验证
      if (formRef.value) {
        formRef.value.validateFields(['fileList']);
      }
    })
    .catch((error) => {
      console.error('上传失败:', error);
      message.error('文件上传失败，请重试');
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};

// 移除文件
const handleRemove = (file: any) => {
  const index = fileList.value.indexOf(file);
  if (index !== -1) {
    fileList.value.splice(index, 1);
  }

  // 更新表单模型
  formModel.value.fileList = [...fileList.value];

  // 触发文件字段的验证
  if (formRef.value) {
    formRef.value.validateFields(['fileList']);
  }
};

// 提交手动调价
const submitManualAdjust = () => {
  if (!formRef.value) return;
  console.log(fileList.value, 'fileList.value');
  if (itemType.value == 1) {
    if (!formModel.value.marketPrice) {
      message.error('请填写市场价')
      return
    };
    if (fileList.value.length == 0) {
      message.error('请上传见证行材料')
      return
    };
  } else if (itemType.value == 2) {
    if (!formModel.value.fullDayMarketPrice) {
      message.error('请填写全天市场价')
      return
    };
    if (!formModel.value.halfDayMarketPrice) {
      message.error('请填写半天市场价')
      return
    };
    if (fileList.value.length == 0) {
      message.error('请上传见证行材料')
      return
    };
  }


  formRef.value
    .validate()
    .then(() => {
      if (itemType.value === 1) {
        // 房间调价 - 更新表格显示数据
        if (currentItem.value) {
          currentItem.value.marketPrice = marketPrice.value;
          if (fileList.value.length > 0) {
            currentItem.value.fileInfo = {
              filePath: fileList.value[0].filePath,
              fileName: fileList.value[0].fileName,
            };
          }
        }

        // 更新原始数据
        if (originalItem.value) {
          if (!originalItem.value.priceResults) {
            originalItem.value.priceResults = [];
          }

          const marketPriceItem = originalItem.value.priceResults.find(
            (item: any) => item.priceItem === PriceTypeConstant.MARKET_PRICE.code,
          );

          if (marketPriceItem) {
            marketPriceItem.price = marketPrice.value;
          } else {
            originalItem.value.priceResults.push({
              priceItem: PriceTypeConstant.MARKET_PRICE.code,
              price: marketPrice.value,
            });
          }

          if (fileList.value && fileList.value.length > 0) {
            originalItem.value.fileInfo = {
              filePath: fileList.value[0].filePath,
              fileName: fileList.value[0].fileName,
            };
          }
        }
        console.log(originalItem.value,"originalItem");
        console.log(currentItem.value,"currentItem");
        console.log(price.value,"price.value");
        
        
        if (priceInquiryData.value?.resourceHotelRooms) {
          const targetRoomId = originalItem.value?.id;
          const targetRoomName = originalItem.value?.roomName;
          if (!targetRoomName && !targetRoomId) return;

          priceInquiryData.value.resourceHotelRooms.some((item) => {
            if (item.id === targetRoomId || item.roomName?.includes(targetRoomName)) {
              item.fileInfo = originalItem.value.fileInfo;
            }
            return false;
          });
          price.value.some((item) => {
            if (item.id === targetRoomId || item.roomName?.includes(targetRoomName)) {
              item.fileInfo = originalItem.value.fileInfo;
            }
            return false;
          });
        }

        message.success('市场价已更新');
        manualAdjustVisible.value = false;
      }
      // 会场调价
      else if (itemType.value === 2) {
        // 更新表格显示数据
        if (currentItem.value) {
          if (fullDayMarketPrice.value !== null) {
            currentItem.value.fullDayMarketPrice = fullDayMarketPrice.value;
          }

          if (halfDayMarketPrice.value !== null) {
            currentItem.value.halfDayMarketPrice = halfDayMarketPrice.value;
          }

          if (fileList.value.length > 0) {
            currentItem.value.fileInfo = {
              filePath: fileList.value[0].filePath,
              fileName: fileList.value[0].fileName,
            };
          }
        }

        // 更新原始数据
        if (originalItem.value) {
          if (!originalItem.value.priceResults) {
            originalItem.value.priceResults = [];
          }

          if (fullDayMarketPrice.value !== null) {
            const fullDayMarketPriceItem = originalItem.value.priceResults.find(
              (item: any) => item.priceItem === PriceTypeConstant.MARKET_PRICE_FULL_DAY.code,
            );

            if (fullDayMarketPriceItem) {
              fullDayMarketPriceItem.price = fullDayMarketPrice.value;
            } else {
              originalItem.value.priceResults.push({
                priceItem: PriceTypeConstant.MARKET_PRICE_FULL_DAY.code,
                price: fullDayMarketPrice.value,
              });
            }
          }
          console.log('66666');

          if (halfDayMarketPrice.value !== null) {
            const halfDayMarketPriceItem = originalItem.value.priceResults.find(
              (item: any) => item.priceItem === PriceTypeConstant.MARKET_PRICE_HALF_DAY.code,
            );

            if (halfDayMarketPriceItem) {
              halfDayMarketPriceItem.price = halfDayMarketPrice.value;
            } else {
              originalItem.value.priceResults.push({
                priceItem: PriceTypeConstant.MARKET_PRICE_HALF_DAY.code,
                price: halfDayMarketPrice.value,
              });
            }
          }

          if (fileList.value.length > 0) {
            originalItem.value.fileInfo = {
              filePath: fileList.value[0].filePath,
              fileName: fileList.value[0].fileName,
            };
          }
        }
        if (priceInquiryData.value?.resourceHotelPlaces) {
          const targetRoomName = originalItem.value?.platformPlaceId;
          if (!targetRoomName) return;

          const found = priceInquiryData.value.resourceHotelPlaces.some((item: { platformPlaceId: any; fileInfo: any; }) => {
            if (item.platformPlaceId === targetRoomName) {
              item.fileInfo = originalItem.value.fileInfo;
            }
            return false;
          });
          const foundNew = Market.value.some((item) => {
            if (item.id === targetRoomName) {
              item.fileInfo = originalItem.value.fileInfo;
            }
            // console.log('66666');
            return false;
          });
          if (!found) console.warn(`未找到指定会场：${targetRoomName}`);
          if (!foundNew) console.warn(`未找到指定会场：${targetRoomName}`);
        }

        message.success('市场价已更新');
        // console.log(priceInquiryData.value.resourceHotelPlaces, "priceInquiryData.value.resourceHotelPlaces");
        // console.log(Market.value, "Market.value");


      }
      manualAdjustVisible.value = false;
    })
    .catch(() => {
      // 验证失败，不做任何操作，错误信息会自动显示
    });
};

// 取消手动调价
const cancelManualAdjust = () => {
  formRef.value?.clearValidate()
  manualAdjustVisible.value = false;
};

// 收集所有调价数据
const collectAdjustmentData = () => {
  // console.log(price.value, 'price.value');

  // 按照Swagger文档要求的数据结构
  const submitData = {
    hotelPriceInquiryRooms: [] as any[],
    hotelPriceInquiryPlaces: [] as any[],
  };

  // 收集房间数据
  if (price?.value) {
    // console.log(priceInquiryData.value, 'priceInquiryData.value');

    price.value.forEach((room: any, index) => {
      const marketPriceItem = room.priceResults?.find(
        (item: any) => item.priceItem === PriceTypeConstant.MARKET_PRICE.code,
      );

      const PriceItem = priceInquiryData.value.resourceHotelRooms[index].priceResults?.find((item) =>
        item.priceItem === PriceTypeConstant.MARKET_PRICE.code,
      )

      if (marketPriceItem && marketPriceItem.price !== null) {
        const roomItem = {
          id: marketPriceItem.id,
          attachmentId: PriceItem?.attachmentId || null,
          priceItem: 10, // 市场价code为10
          price: marketPriceItem.price,
        } as any;

        // 添加文件信息到单个价格项内部
        if (room.fileInfo) {
          roomItem.type = FileTypeConstant.WITNESS_MATERIALS.code; // 默认为文档类型
          roomItem.attachmentPath = room.fileInfo.filePath;
        } else {
          roomItem.type = FileTypeConstant.WITNESS_MATERIALS.code; // 默认为文档类型
          roomItem.attachmentPath = marketPriceItem.path;
        }

        const roomData = {
          roomId: room.id,
          itemPrices: [roomItem],
        };
        submitData.hotelPriceInquiryRooms.push(roomData);
      }
    });
  }
  console.log(Market?.value, "Market?.value");
  console.log(priceInquiryData.value, 'priceInquiryData.value');
  // 收集会场数据
  if (Market?.value) {
    Market?.value.forEach((venue: any, index) => {
      const fullDayMarketPriceItem = venue.priceResults?.find(
        (item: any) => item.priceItem === PriceTypeConstant.MARKET_PRICE_FULL_DAY.code,
      );
      const halfDayMarketPriceItem = venue.priceResults?.find(
        (item: any) => item.priceItem === PriceTypeConstant.MARKET_PRICE_HALF_DAY.code,
      );

      const fullDayPriceItem = priceInquiryData.value.resourceHotelPlaces[index].priceResults?.find((item) =>
        item.priceItem === PriceTypeConstant.MARKET_PRICE_FULL_DAY.code,
      )
      const halfDayPriceItem = priceInquiryData.value.resourceHotelPlaces[index].priceResults?.find((item) =>
        item.priceItem === PriceTypeConstant.MARKET_PRICE_HALF_DAY.code,
      )

      const itemPrices = [] as any[];

      if (fullDayMarketPriceItem && fullDayMarketPriceItem.price !== null) {
        const fullDayItem = {
          id: fullDayMarketPriceItem.id,
          attachmentId: fullDayPriceItem?.attachmentId || null,
          priceItem: 180, // 市场价全天价code为180
          price: fullDayMarketPriceItem.price,
        } as any;

        // 添加文件信息到价格项内部
        if (venue.fileInfo) {
          fullDayItem.type = FileTypeConstant.WITNESS_MATERIALS.code;
          fullDayItem.attachmentPath = venue.fileInfo.filePath;
        } else {
          fullDayItem.type = FileTypeConstant.WITNESS_MATERIALS.code;
          fullDayItem.attachmentPath = fullDayMarketPriceItem.path;
        }

        itemPrices.push(fullDayItem);
      }

      if (halfDayMarketPriceItem && halfDayMarketPriceItem.price !== null) {
        const halfDayItem = {
          id: halfDayMarketPriceItem.id,
          attachmentId: halfDayPriceItem?.attachmentId || null,
          priceItem: 190, // 市场价半天价code为190
          price: halfDayMarketPriceItem.price,
        } as any;

        // 添加文件信息到价格项内部
        if (venue.fileInfo) {
          halfDayItem.type = FileTypeConstant.WITNESS_MATERIALS.code;
          halfDayItem.attachmentPath = venue.fileInfo.filePath;
        } else {
          halfDayItem.type = FileTypeConstant.WITNESS_MATERIALS.code;
          halfDayItem.attachmentPath = halfDayMarketPriceItem.path;
        }

        itemPrices.push(halfDayItem);
      }

      if (itemPrices.length > 0) {
        const venueData = {
          placeId: venue.id,
          itemPrices: itemPrices,
        };

        submitData.hotelPriceInquiryPlaces.push(venueData);
      }
    });
  }
  console.log(submitData, "submitData");

  return submitData;
};

// 添加市场价验证函数
const validateMarketPrices = (): boolean => {
  console.log(price.value, 'price.value');

  let marketPriceItem = [];
  //获取市场价
  for (const item of price?.value) {
    const found = item.priceResults?.find((item: any) => item.priceItem === PriceTypeConstant.MARKET_PRICE.code);
    marketPriceItem.push(found);
  }
  console.log(marketPriceItem, 'marketPriceItem');

  // 验证房间价格
  if (priceInquiryData.value?.resourceHotelRooms) {
    for (const [index, room] of priceInquiryData.value.resourceHotelRooms.entries()) {
      // //获取市场价
      // const marketPriceItem = room.priceResults?.find(
      //   (item: any) => item.priceItem === PriceTypeConstant.MARKET_PRICE.code
      // );

      if (!marketPriceItem[index] || marketPriceItem[index].price === null) {
        message.error('存在未设置市场价的房间');
        return false;
      }

      // 获取淡季协议价,50人以下
      const lightSeasonAgreementUnder50 = room.priceResults?.find(
        (item: any) => item.priceItem === PriceTypeConstant.LIGHT_SEASON_AGREEMENT_PRICE_UNDER_50.code,
      );

      // 获取淡季协议价,50人以上
      const lightSeasonAgreementAbove50 = room.priceResults?.find(
        (item: any) => item.priceItem === PriceTypeConstant.LIGHT_SEASON_AGREEMENT_PRICE_ABOVE_50.code,
      );

      // 验证市场价是否大于淡季协议价
      if (lightSeasonAgreementUnder50 && marketPriceItem[index].price <= lightSeasonAgreementUnder50.price) {
        message.error(`房间"${getRoomTypeName(room.roomType)}"的市场价需大于淡季协议价`);
        return false;
      }
      // 获取淡季协议价,50人以上
      const lightSeasonTier = room.priceResults?.find(
        (item: any) => item.priceItem === PriceTypeConstant.AGREEMENT_PRICE.code,
      );

      // 验证市场价是否大于淡季协议价
      if (lightSeasonTier && marketPriceItem[index].price <= lightSeasonTier.price) {
        message.error(`房间"${getRoomTypeName(room.roomType)}"的市场价需大于协议价`);
        return false;
      }

      if (lightSeasonAgreementAbove50 && marketPriceItem[index].price <= lightSeasonAgreementAbove50.price) {
        message.error(`房间"${getRoomTypeName(room.roomType)}"的市场价需大于淡季协议价`);
        return false;
      }
    }
  }

  return true;
};

// 添加见证性材料验证函数
const validateWitnessMaterials = (): boolean => {
  // 验证房间见证性材料
  console.log(priceInquiryData.value?.resourceHotelRooms, '提交的住宿价格');
  if (priceInquiryData.value?.resourceHotelRooms) {
    for (const room of priceInquiryData.value.resourceHotelRooms) {
      // 获取市场价
      const marketPriceItem = room.priceResults?.find(
        (item: any) => item.priceItem === PriceTypeConstant.MARKET_PRICE.code,
      );

      // 如果有市场价，但没有上传见证性材料
      if ((marketPriceItem && marketPriceItem.price !== null && !marketPriceItem.path) && !room?.fileInfo?.filePath) {
        message.error(`房间"${getRoomTypeName(room.roomType)}"的市场价需上传见证性材料`);
        return false;
      }
    }
  }

  // 验证会场见证性材料
  if ((priceInquiryData.value as any)?.resourceHotelPlaces && isLocal.value) {
    console.log(priceInquiryData.value.resourceHotelPlaces, "提交的会场价格");

    for (const venue of (priceInquiryData.value as any).resourceHotelPlaces) {
      // 获取全天市场价
      const fullDayMarketPriceItem = venue.priceResults?.find(
        (item: any) => item.priceItem === PriceTypeConstant.MARKET_PRICE_FULL_DAY.code,
      );

      // 获取半天市场价
      const halfDayMarketPriceItem = venue.priceResults?.find(
        (item: any) => item.priceItem === PriceTypeConstant.MARKET_PRICE_HALF_DAY.code,
      );


      // 如果有市场价（全天或半天），但没有上传见证性材料
      if (!fullDayMarketPriceItem?.path && !halfDayMarketPriceItem?.path && !venue?.fileInfo?.filePath) {
        message.error(`会场"${venue.placeName}"的市场价需上传见证性材料`);
        return false;
      }
    }
  }

  return true;
};

const confirmLoading = ref(false)
// 添加验证会场市场价函数
const validateVenueMarketPrices = (): boolean => {

  let fullDayMarketPriceItem = [];
  for (const venue of Market?.value) {
    const found = venue.priceResults?.find(
      (item: any) => item.priceItem === PriceTypeConstant.MARKET_PRICE_FULL_DAY.code,
    );
    fullDayMarketPriceItem.push(found);
  }
  let halfDayMarketPriceItem = [];
  for (const venue of Market?.value) {
    const found = venue.priceResults?.find(
      (item: any) => item.priceItem === PriceTypeConstant.MARKET_PRICE_HALF_DAY.code,
    );
    halfDayMarketPriceItem.push(found);
  }
  // 验证会场价格
  if ((priceInquiryData.value as any)?.resourceHotelPlaces) {
    for (const [index, venue] of (priceInquiryData.value as any).resourceHotelPlaces.entries()) {
      // // 获取全天市场价
      // const fullDayMarketPriceItem = venue.priceResults?.find(
      //   (item: any) => item.priceItem === PriceTypeConstant.MARKET_PRICE_FULL_DAY.code
      // );

      // // 获取半天市场价
      // const halfDayMarketPriceItem = venue.priceResults?.find(
      //   (item: any) => item.priceItem === PriceTypeConstant.MARKET_PRICE_HALF_DAY.code
      // );

      // 检查是否两个价格都填写了
      if (!fullDayMarketPriceItem[index] || fullDayMarketPriceItem[index].price === null) {
        message.error(`会场"${venue.placeName}"的全天市场价必须填写`);
        return false;
      }

      if (!halfDayMarketPriceItem[index] || halfDayMarketPriceItem[index].price === null) {
        message.error(`会场"${venue.placeName}"的半天市场价必须填写`);
        return false;
      }

      // 检查是否上传了见证性材料
      if (!halfDayMarketPriceItem[index] || halfDayMarketPriceItem[index].path === null) {
        message.error(`会场"${venue.placeName}"必须上传见证性材料`);
        return false;
      }


      if (fullDayMarketPriceItem[index] && fullDayMarketPriceItem[index].price !== null) {
        /*淡旺季全天市场价比较*/
        if (enablejudgment.value && isLocal.value) {
          // 淡季全天协议价
          const lightSeasonFullDayAgreementPrice = venue.priceResults?.find(
            (item: any) => item.priceItem === PriceTypeConstant.LIGHT_SEASON_FULL_DAY_AGREEMENT_PRICE.code,
          );
          //旺季全天价协议价
          const OrdinaryRegularfulldayAgreementprice = venue.priceResults?.find(
            (item: any) => item.priceItem === PriceTypeConstant.PEAK_SEASON_FULL_DAY_AGREEMENT_PRICE.code,
          );

          if (OrdinaryRegularfulldayAgreementprice && fullDayMarketPriceItem[index].price <= OrdinaryRegularfulldayAgreementprice.price) {
            message.error(`会场"${venue.placeName}"的全天市场价需大于旺季全天协议价`);
            return false
          }
          if (lightSeasonFullDayAgreementPrice && fullDayMarketPriceItem[index].price <= lightSeasonFullDayAgreementPrice.price) {
            message.error(`会场"${venue.placeName}"的全天市场价需大于淡季全天协议价`);
            return false
          }
          /*无淡旺季 */
        } else if (!enablejudgment.value && isLocal.value) {
          //全天协议价
          const OrdinaryRegularfulldayAgreementprice = venue.priceResults?.find(
            (item: any) => item.priceItem === PriceTypeConstant.AGREEMENT_PRICE_HALF_DAY.code,
          );
          if (OrdinaryRegularfulldayAgreementprice && fullDayMarketPriceItem[index].price <= OrdinaryRegularfulldayAgreementprice.price) {
            message.error(`会场"${venue.placeName}"的全天市场价需全天协议价`);
            return false
          }
        }
      }

      // 如果有半天市场价，验证是否大于淡季和旺季半天协议价
      if (halfDayMarketPriceItem[index] && halfDayMarketPriceItem[index].price !== null) {
        if (enablejudgment.value && isLocal.value) {
          // 淡季半天协议价
          const peakSeasonFullDayAgreementPrice = venue.priceResults?.find(
            (item: any) => item.priceItem === PriceTypeConstant.LIGHT_SEASON_HALF_DAY_AGREEMENT_PRICE.code,
          );
          // 旺季半天协议价
          const peakSeasonHalfDayAgreementPrice = venue.priceResults?.find(
            (item: any) => item.priceItem === PriceTypeConstant.PEAK_SEASON_HALF_DAY_AGREEMENT_PRICE.code,
          );
          if (peakSeasonFullDayAgreementPrice && halfDayMarketPriceItem[index].price <= peakSeasonFullDayAgreementPrice.price) {
            message.error(`会场"${venue.placeName}"的半天市场价需大于淡季半天协议价`);
            return false
          }
          if (peakSeasonHalfDayAgreementPrice && halfDayMarketPriceItem[index].price <= peakSeasonHalfDayAgreementPrice.price) {
            message.error(`会场"${venue.placeName}"的半天市场价需大于旺季半天协议价`);
            return false
          }
          /* 无淡旺季 */
        } else if (!enablejudgment.value && isLocal.value) {
          //全天协议价
          const OrdinaryRegularfulldayAgreementprice = venue.priceResults?.find(
            (item: any) => item.priceItem === PriceTypeConstant.AGREEMENT_PRICE_FULL_DAY.code,
          );
          if (OrdinaryRegularfulldayAgreementprice && halfDayMarketPriceItem[index].price <= OrdinaryRegularfulldayAgreementprice.price) {
            message.error(`会场"${venue.placeName}"的半天市场价需半天协议价`);
            return false
          }
        }
      }
    }
  }

  return true;
};

// 修改提交函数，添加验证逻辑
const handleSubmit = (isConfirm: boolean) => {
  console.log(isLocal);

  // if (!priceInquiryData.value?.id) {
  //   message.error('无法获取价格询价单ID');
  //   return;
  // }

  // // 只有确认才需要验证
  if (isConfirm) {
    if (isLocal.value) {
      // 验证房间价格
      if (!validateMarketPrices()) {
        return;
      }

      // 验证会场价格
      if (!validateVenueMarketPrices()) {
        return;
      }
    }
    // 验证见证性材料
    if (!validateWitnessMaterials()) {
      return;
    }
  }

  // 收集所有调价数据
  const { hotelPriceInquiryRooms, hotelPriceInquiryPlaces } = collectAdjustmentData();

  const requestData = {
    result: isConfirm,
    id: priceInquiryData.value?.id,
    hotelPriceInquiryRooms,
    hotelPriceInquiryPlaces,
  };

  console.log('调价数据汇总:', requestData);

  // // 显示确认弹窗
  hModal.confirm({
    title: isConfirm ? '确认并送审' : '驳回',
    content: isConfirm ? '确定要确认并送审吗？' : '确定要驳回吗？',
    okText: '确定',
    cancelText: '取消',
    confirmLoading: confirmLoading.value,
    onOk: () => {
      confirmLoading.value = true
      priceInquiryApi.userSubmit(requestData as any).then((result) => {
        console.log(result, 'result');
        message.success(isConfirm ? '确认并送审成功' : '驳回成功');
        if (result) {
          approveCode.value = result;
          console.log(approveCode.value, 'approveCode.value');

          approvalModalShow.value = true;
        } else {
          // 如果没有审批流程，直接跳转到列表页
          router.push('/bidman/priceInquiry/index');
        }
        confirmLoading.value = false
      })
        .finally(() => {
          this.confirmLoading = false
        })
    },
  });
};
</script>

<template>
  <div class="price-inquiry-detail" :class="{ 'wide-padding': hideBtn === '1' }">
    <!-- 页面标题 -->
    <div class="page-title">
      <h-row>
        <h-col :span="24" class="title-container">
          <h1>{{ priceInquiryData?.platformHotelName }}价格单</h1>
        </h-col>
      </h-row>
    </div>

    <!-- 主体内容 -->
    <div class="content-container">
      <div class="Modify">
        <div v-if="isLocal && enableQuarterSetting">
          <!-- 价格时段选择 -->
          <h-row class="price-period-section" v-if="enableQuarterSetting">
            <h-col :span="3" class="label">价格时间段：</h-col>
            <h-col :span="21">
              <h-radio-group v-model:value="enablejudgment" disabled>
                <h-radio :value="QuarterSettingTypeConstant.ENABLE.value">{{
                  QuarterSettingTypeConstant.ENABLE.name
                }}</h-radio>
                <h-radio :value="QuarterSettingTypeConstant.DISABLE.value">{{
                  QuarterSettingTypeConstant.DISABLE.name
                }}</h-radio>
              </h-radio-group>
            </h-col>
          </h-row>
          <!-- 价格时段列表（淡季） -->
          <h-row class="period-item" style="text-align: right" v-if="resourceHotelQuarters.length > 0">
            <h-col :span="5" class="label Modifylabel" style="font-size: 14px">淡季时间：</h-col>
            <h-col :span="18" class="period-inputs">
              <div v-for="(quarter, index) in getLightSeasonQuarters()" :key="'light-' + index"
                class="period-input-item">
                <h-select :value="`${quarter.startDate} 至 ${quarter.endDate}`" disabled style="width: 250px">
                  <h-select-option :value="`${quarter.startDate} 至 ${quarter.endDate}`">{{ quarter.startDate }} 至 {{
                    quarter.endDate }}</h-select-option>
                </h-select>
              </div>
            </h-col>
          </h-row>

          <!-- 价格时段列表（旺季） -->
          <h-row class="period-item" style="text-align: right" v-if="resourceHotelQuarters.length > 0">
            <h-col :span="5" class="label Modifylabel" style="font-size: 14px">旺季时间：</h-col>
            <h-col :span="18" class="period-inputs">
              <div v-for="(quarter, index) in getPeakSeasonQuarters()" :key="'peak-' + index" class="period-input-item">
                <h-select :value="`${quarter.startDate} 至 ${quarter.endDate}`" disabled style="width: 250px">
                  <h-select-option :value="`${quarter.startDate} 至 ${quarter.endDate}`">{{ quarter.startDate }} 至 {{
                    quarter.endDate }}</h-select-option>
                </h-select>
              </div>
            </h-col>
          </h-row>
        </div>

        <!-- 价格阶梯设置 -->
        <!-- <h-row class="price-tier-section" v-if="enableQuarterSetting">
          <h-col :span="3" class="label">价格阶梯设置：</h-col>
          <h-col :span="21">
            <h-radio-group v-model:value="enableStairsSetting" disabled>
              <h-radio :value="PriceTierSettingTypeConstant.ENABLE.value" checked>{{
                PriceTierSettingTypeConstant.ENABLE.name }}</h-radio>
              <h-radio :value="PriceTierSettingTypeConstant.DISABLE.value">{{ PriceTierSettingTypeConstant.DISABLE.name
              }}</h-radio>
            </h-radio-group>
          </h-col>
        </h-row> -->

        <!-- 价格阶梯范围 -->
        <!-- <h-row class="tier-range-section" v-if="enableQuarterSetting">
            <h-col :span="4" class="label">第一阶梯会议人数：</h-col>
            <h-col :span="20">
              <h-input-number :value="1" :min="1" :disabled="true" style="width: 80px" />
              <span class="separator">–</span>
              <h-input-number :value="50" :min="1" :disabled="true" style="width: 80px" />
            </h-col>
          </h-row>

          <h-row class="tier-range-section" v-if="enableQuarterSetting">
            <h-col :span="4" class="label">第二阶梯会议人数：</h-col>
            <h-col :span="20">
              <h-input-number :value="51" :min="1" :disabled="true" style="width: 80px" />
              <span class="text">及以上</span>
            </h-col>
          </h-row>
        </div>
      </div> -->

        <!-- 价格表格 -->
        <h-row class="price-table-section Modify-Table" v-if="isLocal">
          <h-col :span="24">
            <h3 class="sub-section-title">住宿：</h3>
            <h-table :columns="tableColumns" :dataSource="processedRoomData" :pagination="false" bordered>
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'operation'">
                  <a style="color: #1890ff" @click="() => handleManualAdjust(record, null)">手动调价</a>
                </template>
              </template>
            </h-table>
          </h-col>
        </h-row>

        <h-row class="price-table-section Modify-Table">
          <h-col :span="24">
            <h3 class="sub-section-title">会场：</h3>
            <h-table :columns="venueTableColumns" :dataSource="processedVenueData" :pagination="false" bordered
              :scroll="{ x: 3000 }">
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'operation'">
                  <a style="color: #1890ff" @click="handleVenueManualAdjust(record)">手动调价</a>
                </template>
              </template>
            </h-table>
          </h-col>
        </h-row>
      </div>

      <!-- 底部固定操作栏 -->
      <div class="footer-actions" v-if="showSubmitActions">
        <h-button type="primary" style="background-color: #ff4d4f; border-color: #ff4d4f"
          @click="() => handleSubmit(false)">驳回</h-button>
        <h-button type="primary" @click="() => handleSubmit(true)">确认并送审</h-button>
      </div>

      <!-- 手动调价弹窗 -->
      <h-modal :visible="manualAdjustVisible" @update:visible="manualAdjustVisible = $event"
        :title="itemType === 1 ? '住宿手动调价' : '会场手动调价'" width="500px" :footer="null">
        <h-form ref="formRef" :model="formModel" :rules="formRules" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }"
          :requiredMark="false">
          <!-- 房间调价时显示 -->
          <template v-if="itemType === 1">
            <h-form-item label="市场价" name="marketPrice">
              <h-input-number v-model:value="formModel.marketPrice" @change="handlePriceChange" style="width: 100%"
                :precision="2" />
            </h-form-item>
          </template>

          <!-- 会场调价时显示 -->
          <template v-if="itemType === 2">
            <h-form-item label="全天市场价" name="fullDayMarketPrice">
              <h-input-number v-model:value="formModel.fullDayMarketPrice" @change="handleFullDayPriceChange"
                style="width: 100%" :precision="2" />
            </h-form-item>
            <h-form-item label="半天市场价" name="halfDayMarketPrice">
              <h-input-number v-model:value="formModel.halfDayMarketPrice" @change="handleHalfDayPriceChange"
                style="width: 100%" :precision="2" />
            </h-form-item>
            <div style="color: #999; font-size: 12px; margin-bottom: 16px; padding-left: 152px">

            </div>
          </template>

          <h-form-item label="见证性材料" name="fileList">
            <h-upload :file-list="fileList" :custom-request="uploadRequest" :multiple="false" :max-count="1"
              @remove="handleRemove" accept=".pdf, .doc, .docx, .jpg, .png, .jpeg, .xls, .xlsx"
              :show-upload-list="true">
              <h-button>
                <template #icon>
                  <UploadOutlined />
                </template>
                上传文件
              </h-button>
            </h-upload>
          </h-form-item>
        </h-form>
        <div class="footer-btns" style="text-align: right; margin-top: 16px">
          <h-button @click="cancelManualAdjust">取消</h-button>
          <h-button type="primary" style="margin-left: 8px" @click="submitManualAdjust"
            :loading="uploadLoading">确定</h-button>
        </div>
      </h-modal>
    </div>
    <!-- 添加独立的预览弹窗 -->
    <div class="global-preview-overlay" v-if="previewVisible" @click="previewVisible = false">
      <div class="global-preview-container" @click.stop>
        <div class="global-preview-header">
          <span>{{ previewTitle }}</span>
          <span v-if="isShowTitle">{{ imgTitle }}</span>
          <button class="global-preview-close" @click="previewVisible = false">×</button>
        </div>
        <div class="global-preview-main">
          <img :src="previewImage" alt="预览图片" />
        </div>
        <div class="global-preview-nav" v-if="previewImages.length > 1">
          <div v-for="(img, i) in previewImages" :key="i" class="global-preview-thumbnail"
            :class="{ active: img.src === previewImage }" @click="modifyPicture(img)">
            <img :src="img.src" :alt="img.title" />
          </div>
        </div>
      </div>
    </div>
    <!-- 审批流程模态框 -->
    <h-modal v-model:open="approvalModalShow" title="已提交如下人员审批" width="80%" :keyboard="false" :maskClosable="false"
      :closable="false">
      <div>
        <iframe width="100%" :src="businessProcess + '?code=' + approveCode + '#/detailsPcSt'" frameborder="0"></iframe>
      </div>
      <template #footer>
        <h-button @click="
          approvalModalShow = false;
        router.push('/bidman/priceInquiry/index');
        ">确定</h-button>
      </template>
    </h-modal>
  </div>
</template>

<style lang="less" scoped>
.price-inquiry-detail {
  width: 100%;
  height: 100%;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  position: relative;
  // padding-bottom: 60px; // 为底部操作栏预留空间

  &.wide-padding {
    .content-container {
      padding: 16px 300px;
    }
  }

  .page-title {
    padding: 16px 0;
    border-bottom: 1px solid #f0f0f0;

    .title-container {
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;

      h1 {
        font-size: 20px;
        font-weight: 500;
        margin: 0;
        text-align: center;
      }
    }
  }

  .content-container {
    flex: 1;
    overflow-y: auto;

    .section-title {
      margin-bottom: 16px;

      h2 {
        font-size: 16px;
        font-weight: 500;
        margin: 0;
      }
    }

    .sub-section-title {
      font-size: 15px;
      font-weight: 500;
      margin: 0 0 12px 0;
    }

    .label {
      font-size: 16px;
      line-height: 32px;
      padding-right: 8px;
    }

    .ant-radio-wrapper {
      margin-right: 90px;
    }

    .Modifylabel {
      display: block;
      flex: 0 0 18.833333%;
      max-width: 18.833333%;
    }

    .price-period-section {
      display: flex;
      align-items: center;
    }

    .price-period-section,
    .price-tier-section,
    .tier-range-section,
    .period-item {
      margin-bottom: 16px;
    }

    .period-inputs {
      display: flex;
      flex-wrap: wrap;

      .period-input-item {
        margin-right: 12px;
        margin-bottom: 8px;

        :deep(.ant-select-selection-item) {
          flex: none !important;
          text-align: right !important;
        }
      }
    }

    .separator {
      margin: 0 8px;
    }

    .text {
      margin-left: 8px;
      line-height: 32px;
    }

    .price-table-section {
      margin-top: 24px;
    }

    // 市场价单元格样式
    :deep(.market-price-cell) {
      display: flex;
      align-items: center;
      justify-content: center;

      .market-price-value {
        margin-right: 4px;
      }
    }
  }

  .footer-actions {
    height: 60px;
    background-color: #fff;
    border-top: 1px solid #f0f0f0;
    padding: 12px 24px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    z-index: 10;

    button {
      margin: 0 8px;
      min-width: 80px;
    }
  }
}

.Modify {
  padding: 20px;
  box-shadow: 0px 3px 12px 0px rgba(1, 12, 51, 0.145);
}

.Modify-Table {
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0px 3px 12px 0px rgba(1, 12, 51, 0.145);
}

/* 全局预览弹窗样式 */
.global-preview-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.global-preview-container {
  background-color: white;
  border-radius: 4px;
  width: 90%;
  max-width: 800px;
  height: 500px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.global-preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #eee;
}

.global-preview-close {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #999;
}

.global-preview-main {
  padding: 16px;
  text-align: center;
  overflow: auto;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    display: block;
    max-width: 100%;
    max-height: 60vh;
    object-fit: contain;
    margin: auto;
  }
}

.global-preview-nav {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 16px;
  justify-content: center;
  border-top: 1px solid #eee;
  background-color: #f9f9f9;
}

.global-preview-thumbnail {
  width: 60px;
  height: 60px;
  border: 2px solid transparent;
  cursor: pointer;
  overflow: hidden;

  &.active {
    border-color: #1890ff;
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  &:hover {
    opacity: 0.8;
  }
}
</style>
