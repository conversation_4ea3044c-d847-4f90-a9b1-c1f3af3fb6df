<script setup lang="ts">
// 需求确认
import { onMounted, ref, inject } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { useRoute, useRouter } from 'vue-router';
import { errorModal, resolveParam } from '@haierbusiness-front/utils';
import advisors from '@haierbusiness-front/components/mice/advisors/index.vue';

import { demandApi } from '@haierbusiness-front/apis';

const route = useRoute();
const router = useRouter();
const isCloseLastTab = inject<Ref<boolean>>('isCloseLastTab');

const businessIndex = import.meta.env.VITE_BUSINESS_INDEX_URL + '#';
const businessProcess = import.meta.env.VITE_BUSINESS_PROCESS_URL;

const previewSource = ref<string>('demandContrast'); // 需求交互，对比
const miceId = ref<string>('');
const hideBtn = ref<string>('');
const demandRejectReason = ref<string>(''); // 驳回原因
const approveCode = ref<string>(''); // 审批流Code
const approvalModalShow = ref<boolean>(false);
const refuseModalShow = ref<boolean>(false);
const subLoading = ref<boolean>(false);
const refuseLoading = ref<boolean>(false);

// 驳回
const refuseBtn = () => {
  demandRejectReason.value = '';
  refuseModalShow.value = true;
};
const handleRefuse = async () => {
  if (!demandRejectReason.value) {
    message.error('驳回原因不能为空！');
    return;
  }

  refuseLoading.value = true;

  const res = await demandApi.demandUserReject(
    { miceId: miceId.value, demandRejectReason: demandRejectReason.value },
    (error) => {
      refuseLoading.value = false;
      errorModal(error?.message);

      return;
    },
  );

  refuseLoading.value = false;

  message.success('需求已驳回');

  closeApproval();
};

// 确认需求
const subDemand = async () => {
  Modal.confirm({
    title: '确定提交？',
    // icon: null,
    content: '',
    onOk: async () => {
      subLoading.value = true;

      const res = await demandApi.demandUserConfirm({ miceId: miceId.value }, (error) => {
        subLoading.value = false;
        errorModal(error?.message);

        return;
      });

      subLoading.value = false;

      if (res.data) {
        approvalModalShow.value = true;

        // 审批Code赋值
        approveCode.value = res.data.processCode;

        return;
      }

      // 没有返回审批流Code
      closeApproval();
    },
    onCancel() {},
  });
};

const closeApproval = () => {
  if (isCloseLastTab) {
    // 关闭当前页面
    isCloseLastTab.value = true;
  }

  const url = businessIndex + '/card-order/miceOrder';
  window.location.replace(url);
};

// 返回
const backBtn = () => {
  router.go(-1);
};

onMounted(async () => {
  const record = resolveParam(route.query.record);
  miceId.value = record.miceId;
  hideBtn.value = record.hideBtn || '';
  console.log('%c [ record ]-需求确认', 'font-size:13px; background:pink; color:#bf2c9f;', record);
});
</script>

<template>
  <!-- 需求确认 -->
  <div class="wid1280 demand_preview">
    <advisors :preview-source="previewSource" :isManagePage="false" :platformType="'user'">
      <template #header> </template>
      <template #footer>
        <a-button size="small" class="mr10" @click="backBtn()">返回</a-button>
        <div v-if="hideBtn !== '1'" style="display: inline-block">
          <a-button type="primary" danger class="mr10" size="small" @click="refuseBtn()">需求驳回</a-button>
          <a-button type="primary" size="small" :loading="subLoading" @click="subDemand()">确认需求</a-button>
        </div>
      </template>
    </advisors>

    <!-- 审批流 -->
    <a-modal
      v-model:open="approvalModalShow"
      title="已提交如下人员审批"
      width="80%"
      :keyboard="false"
      :maskClosable="false"
      :closable="false"
    >
      <div>
        <iframe width="100%" :src="businessProcess + '?code=' + approveCode + '#/detailsPcSt'" frameborder="0"></iframe>
      </div>
      <template #footer>
        <a-button @click="closeApproval">确定</a-button>
      </template>
    </a-modal>
    <!-- 驳回 -->
    <a-modal
      v-model:open="refuseModalShow"
      title="驳回原因"
      width="600px"
      :confirmLoading="refuseLoading"
      @ok="handleRefuse"
    >
      <div>
        <a-textarea
          class="mt10 mb10"
          v-model:value="demandRejectReason"
          placeholder="请填写驳回原因"
          :maxlength="500"
        />
      </div>
    </a-modal>
  </div>
</template>

<style scoped lang="less">
.demand_preview {
}
</style>
