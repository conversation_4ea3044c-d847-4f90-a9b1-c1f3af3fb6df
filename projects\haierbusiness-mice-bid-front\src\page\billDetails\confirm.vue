<script setup lang="ts">
import { ref, inject, onMounted, reactive } from 'vue';
import schemeInteract from '@haierbusiness-front/components/scheme/schemeInteract.vue';
import { meetingProcessOrchestration, resolveParam } from '@haierbusiness-front/utils';
import { Button, Dropdown, Menu, Pagination, BadgeRibbon, Tooltip, Popover, message, Modal } from 'ant-design-vue';
import { useRouter, useRoute } from 'vue-router';
import { miceBidManOrderListApi } from '@haierbusiness-front/apis';
import meetingDetail from '@haierbusiness-front/components/mice/orderList/meetingDetail.vue';
import billUploadScheme from '@haierbusiness-front/components/billUploadScheme/billUploadschemeDetails.vue';
const route = useRoute();
const frameModel = ref(inject<any>('frameModel'));
const routeQuery = reactive({
  record: resolveParam(route.query.record) || JSON.parse(route.query.record),
});
const viewSelect = ref('demand');
const presentConfirm = async () => {
  Modal.confirm({
    title: '账单确认',
    content: '是否确认全部账单？',
    async onOk() {
      const res = await miceBidManOrderListApi.userConfirm({
        miceId: routeQuery.record.miceId,
        confirmState: 1,
      });
      console.log(res);
      if (res.success) {
        message.success('确认成功！');
        const businessMiceBid = import.meta.env.VITE_BUSINESS_INDEX_URL + '#';
        // 跳转需求确认页面
        const url =
          (window.location.href.includes('/localhost') ? 'http://localhost:5183/#' : businessMiceBid) +
          '/card-order/miceOrder';
        window.location.href = url;
      }
    },
    onCancel() {
      console.log('Cancel');
    },
    class: 'test',
  });
};
const open = ref(false);
const reason = ref<string>(''); // 驳回原因

const cancelConfirm = async () => {
  if (reason.value === '') {
    message.error('驳回原因不能为空！');
    return;
  }
  const res = await miceBidManOrderListApi.infoConfirm({
    mainCode: routeQuery.record.mainCode,
    confirmStatus: 3,
    rejectReason: reason.value,
  });
  console.log(res);
  if (res.success) {
    message.success('驳回成功！');
    const businessMiceBid = import.meta.env.VITE_BUSINESS_INDEX_URL + '#';
    // 跳转需求确认页面
    const url =
      (window.location.href.includes('/localhost') ? 'http://localhost:5183/#' : businessMiceBid) +
      '/card-order/miceOrder';
    window.location.href = url;
  }
};
onMounted(() => {
  frameModel.value = routeQuery.record.hideBtn === '1' ? 1 : 0;
  console.log('frameModel.value', frameModel.value);
});
</script>

<template>
  <div class="container">
    <!-- 方案互动 -->
    <meetingDetail v-if="viewSelect === 'demand'" type="user" />
    <billUploadScheme v-else-if="viewSelect === 'billUpload'" :platformType="'user'"></billUploadScheme>
    <div class="footer">
      <a-radio-group v-model:value="viewSelect">
        <a-radio-button value="demand">需求视图</a-radio-button>
        <a-radio-button value="billUpload">账单视图</a-radio-button>
      </a-radio-group>
      <div v-if="viewSelect === 'demand'">
        <a-button type="primary" @click="viewSelect = 'billUpload'">下一步</a-button>
      </div>
      <div v-else>
        <a-button style="margin-right: 20px" type="primary" danger @click="open = true">驳回</a-button>
        <a-button type="primary" @click="presentConfirm">确认</a-button>
      </div>
      <a-modal v-model:open="open" title="驳回原因" @ok="cancelConfirm">
        <a-textarea v-model:value="reason" :rows="4" :maxLength="200" placeholder="请输入驳回原因" />
      </a-modal>
    </div>
  </div>
</template>

<style scoped lang="less">
.container {
  padding-bottom: 40px;
}
.footer {
  border-top: 1px solid #ccc;
  right: 0;
  background: #fff;
  z-index: 11;
  width: 100%;
  padding: 10px 20px;
  position: fixed;
  bottom: 0;
  display: flex;
  justify-content: space-between;
}
</style>
