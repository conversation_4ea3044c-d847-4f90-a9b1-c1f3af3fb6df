export enum ServiceProviderType {
  HOTEL = '酒店',
  TRAVEL_AGENCY = '旅行社',
  ZHONGMIAO = '众淼',
}

export enum ServiceProviderState {
  NORMAL = 1,
  FROZEN = 2,
  DISABLED = 3,
}

// 服务商状态（数字型）
export enum ServiceProviderStateEnum {
  MANUAL_START = 0, // 解冻
  MANUAL_STOP = 1, // 冻结
}
export const ServiceProviderStateMap = {
  [ServiceProviderStateEnum.MANUAL_START]: '正常',
  [ServiceProviderStateEnum.MANUAL_STOP]: '冻结',
} as const;

// 违规状态
export enum ExamineStateEnum {
  UNPROCESSED = 10,
  PROCESSED = 20,
  REJECTED = 30,
  CANCELLED = 40,
}
export const ExamineStateMap = {
  [ExamineStateEnum.UNPROCESSED]: '审批中',
  [ExamineStateEnum.PROCESSED]: '已取消',
  [ExamineStateEnum.REJECTED]: '已生效',
  [ExamineStateEnum.CANCELLED]: '已驳回',
} as const;

// 违规类型
export enum ViolationTypeEnum {
  PUNISH = 1,
  RECTIFY = 2,
  RECTIFY_AND_PUNISH = 3,
}
export const ViolationTypeMap = {
  [ViolationTypeEnum.PUNISH]: '扣分+罚款',
  [ViolationTypeEnum.RECTIFY]: '扣分',
  [ViolationTypeEnum.RECTIFY_AND_PUNISH]: '罚款',
} as const;

// 考核状态
export enum AssessmentStatusEnum {
  APPROVING = 10, // 审批中
  CANCELLED = 20, // 已取消
  EFFECTIVE = 30, // 已生效
}
export const AssessmentStatusMap = {
  [AssessmentStatusEnum.APPROVING]: '审批中',
  [AssessmentStatusEnum.CANCELLED]: '已取消',
  [AssessmentStatusEnum.EFFECTIVE]: '已生效',
} as const;

// 违规状态
export enum PunishmentStatusEnum {
  PROCESSING = 10, // 处理中
  PENDING_PAYMENT = 20, // 待收款确认
  PROCESSED = 30, // 已处理
  REJECTED = 40, // 已驳回
  UNPROCESSED = 50, // 未处理
  CANCELLED = 60, // 已取消
}

export const PunishmentStatusMap = {
  [PunishmentStatusEnum.PROCESSING]: '处理中',
  [PunishmentStatusEnum.PENDING_PAYMENT]: '待收款确认',
  [PunishmentStatusEnum.PROCESSED]: '已处理',
  [PunishmentStatusEnum.REJECTED]: '已驳回',
  [PunishmentStatusEnum.UNPROCESSED]: '未处理',
  [PunishmentStatusEnum.CANCELLED]: '已取消',
} as const;

// 保证金状态
export enum MarginStatusEnum {
  COMPLETED = 10, // 已完成
  REJECTED = 11, // 已驳回
  UNDER_VERIFICATION = 20, // 核对中
}

export const MarginStatusMap = {
  [MarginStatusEnum.COMPLETED]: '已完成',
  [MarginStatusEnum.REJECTED]: '已驳回',
  [MarginStatusEnum.UNDER_VERIFICATION]: '核对中',
} as const;

/**
 * 获取违规状态描述
 * @param code 状态码
 * @returns 状态描述
 */
export const getPunishmentStatusDesc = (code?: number): string => {
  if (!code) return '';
  return PunishmentStatusMap[code as PunishmentStatusEnum] || '';
};

// 合同状态枚举
export enum ContractStateEnum {
  VALID = 0, // 有效
  INVALID = 1, // 失效
}

export const ContractStateMap = {
  [ContractStateEnum.VALID]: '有效',
  [ContractStateEnum.INVALID]: '失效',
} as const;

/**
 * 获取合同状态描述
 * @param code 状态码
 * @returns 状态描述
 */
export const getContractStateDesc = (code?: number): string => {
  if (code === undefined || code === null) return '-';
  return ContractStateMap[code as ContractStateEnum] || '-';
};

// 审批状态枚举
export enum ApprovalStatusEnum {
  CANCELLED = 0, // 取消
  APPROVING = 10, // 审批中
  APPROVED = 20, // 审批通过
  REJECTED = 30, // 驳回
  WITHDRAWN = 40, // 撤回
}

export const ApprovalStatusMap = {
  [ApprovalStatusEnum.CANCELLED]: '取消',
  [ApprovalStatusEnum.APPROVING]: '审批中',
  [ApprovalStatusEnum.APPROVED]: '审批通过',
  [ApprovalStatusEnum.REJECTED]: '驳回',
  [ApprovalStatusEnum.WITHDRAWN]: '撤回',
} as const;

/**
 * 获取审批状态描述
 * @param code 状态码
 * @returns 状态描述
 */
export const getApprovalStatusDesc = (code?: number): string => {
  if (code === undefined || code === null) return '-';
  return ApprovalStatusMap[code as ApprovalStatusEnum] || '-';
};

// 服务商冻结状态枚举
export enum SpecialpermissionStatusEnum {
  EMERGENCY = 1, // 加急
  MEETING_REPEAL = 2, // 会议作废
  SPECIFY_SCHEME = 3, // 指定方案
  REQUIREMENT_CLEAR = 4, // 需求澄清
  SIGN_IN_PERSON_CORRECTION_APPEAL = 5, // 签到人员修正申诉
  WINNING_SCHEME_TERMINATION = 6, // 中标方案终止
  SINGLE_REQUIREMENT_AMOUNT_INCREASE = 7, // 单项需求金额上浮超过20%
  DOMESTIC_MEETING_MODE_ENTRY = 8, // 国内会议任意模式录入
}

export const SpecialpermissionStatusMap = {
  [SpecialpermissionStatusEnum.EMERGENCY]: '加急',
  [SpecialpermissionStatusEnum.MEETING_REPEAL]: '会议作废',
  [SpecialpermissionStatusEnum.SPECIFY_SCHEME]: '指定方案',
  [SpecialpermissionStatusEnum.REQUIREMENT_CLEAR]: '需求澄清',
  [SpecialpermissionStatusEnum.SIGN_IN_PERSON_CORRECTION_APPEAL]: '冻结审签到人员修正申诉核中',
  [SpecialpermissionStatusEnum.WINNING_SCHEME_TERMINATION]: '中标方案终止',
  [SpecialpermissionStatusEnum.SINGLE_REQUIREMENT_AMOUNT_INCREASE]: '单项需求金额上浮超过20',
  [SpecialpermissionStatusEnum.DOMESTIC_MEETING_MODE_ENTRY]: '国内会议任意模式录入',
} as const;

// 特殊全限枚举
export enum ServiceProviderFreezeStatusEnum {
  FREEZE_APPROVING = 10, // 冻结审核中
  FROZEN = 20, // 已冻结
  UNFREEZE_APPROVING = 30, // 解冻审批中
  UNFROZEN = 40, // 已解冻
  CANCELLED = 50, // 已驳回
}

export const ServiceProviderFreezeStatusMap = {
  [ServiceProviderFreezeStatusEnum.FREEZE_APPROVING]: '冻结审核中',
  [ServiceProviderFreezeStatusEnum.FROZEN]: '已冻结',
  [ServiceProviderFreezeStatusEnum.UNFREEZE_APPROVING]: '解冻审批中',
  [ServiceProviderFreezeStatusEnum.UNFROZEN]: '已解冻',
  [ServiceProviderFreezeStatusEnum.CANCELLED]: '已驳回',
} as const;

/**
 * 获取服务商冻结状态描述
 * @param code 状态码
 * @returns 状态描述
 */
export const getServiceProviderFreezeStatusDesc = (code?: number): string => {
  if (code === undefined || code === null) return '-';
  return ServiceProviderFreezeStatusMap[code as ServiceProviderFreezeStatusEnum] || '-';
};
